#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام إدارة المقاولين
Contractor Management System
"""

import tkinter as tk
from tkinter import ttk, messagebox
import customtkinter as ctk
from datetime import datetime, date
from tkcalendar import DateEntry

class ContractorManager:
    """مدير المقاولين"""
    
    def __init__(self, parent_window, db_manager, current_user):
        self.parent_window = parent_window
        self.db_manager = db_manager
        self.current_user = current_user
        self.window = None
        
        # إعداد الألوان
        self.colors = {
            'primary': '#1f538d',
            'secondary': '#2980b9',
            'success': '#27ae60',
            'warning': '#f39c12',
            'danger': '#e74c3c',
            'light': '#ecf0f1',
            'dark': '#2c3e50'
        }
        
    def show_contractor_management(self):
        """عرض نافذة إدارة المقاولين"""
        self.window = ctk.CTkToplevel(self.parent_window)
        self.window.title("إدارة المقاولين")
        self.window.geometry("1200x800")
        self.window.transient(self.parent_window)
        self.window.grab_set()
        
        self.setup_ui()
        self.load_contractors()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # العنوان الرئيسي
        title_label = ctk.CTkLabel(
            self.window,
            text="إدارة المقاولين",
            font=ctk.CTkFont(size=24, weight="bold")
        )
        title_label.pack(pady=20)
        
        # إطار التبويبات
        self.tabview = ctk.CTkTabview(self.window)
        self.tabview.pack(fill="both", expand=True, padx=20, pady=20)
        
        # تبويب المقاولين
        self.contractors_tab = self.tabview.add("المقاولين")
        self.setup_contractors_tab()
        
        # تبويب العقود
        self.contracts_tab = self.tabview.add("العقود")
        self.setup_contracts_tab()
        
        # تبويب التقييمات
        self.evaluations_tab = self.tabview.add("التقييمات")
        self.setup_evaluations_tab()
        
        # تبويب المدفوعات
        self.payments_tab = self.tabview.add("المدفوعات")
        self.setup_payments_tab()
        
    def setup_contractors_tab(self):
        """إعداد تبويب المقاولين"""
        # شريط الأدوات
        toolbar_frame = ctk.CTkFrame(self.contractors_tab)
        toolbar_frame.pack(fill="x", padx=10, pady=10)
        
        # أزرار الأدوات
        add_btn = ctk.CTkButton(
            toolbar_frame,
            text="➕ مقاول جديد",
            command=self.add_contractor,
            width=120,
            height=35,
            fg_color=self.colors['success']
        )
        add_btn.pack(side="right", padx=5, pady=5)
        
        edit_btn = ctk.CTkButton(
            toolbar_frame,
            text="✏️ تعديل",
            command=self.edit_contractor,
            width=100,
            height=35,
            fg_color=self.colors['warning']
        )
        edit_btn.pack(side="right", padx=5, pady=5)
        
        # شريط البحث
        search_frame = ctk.CTkFrame(toolbar_frame, fg_color="transparent")
        search_frame.pack(side="left", padx=5, pady=5)
        
        search_label = ctk.CTkLabel(search_frame, text="البحث:")
        search_label.pack(side="left", padx=5)
        
        self.search_var = tk.StringVar()
        search_entry = ctk.CTkEntry(
            search_frame,
            textvariable=self.search_var,
            placeholder_text="اسم المقاول أو الشركة...",
            width=200
        )
        search_entry.pack(side="left", padx=5)
        search_entry.bind('<KeyRelease>', self.search_contractors)
        
        # جدول المقاولين
        table_frame = ctk.CTkFrame(self.contractors_tab)
        table_frame.pack(fill="both", expand=True, padx=10, pady=10)
        
        # أعمدة الجدول
        columns = ("ID", "اسم المقاول", "اسم الشركة", "التخصص", "الهاتف", "البريد الإلكتروني", "التقييم", "الحالة")
        
        self.contractors_tree = ttk.Treeview(table_frame, columns=columns, show="headings", height=20)
        
        # تعريف العناوين
        for col in columns:
            self.contractors_tree.heading(col, text=col)
            self.contractors_tree.column(col, width=120, anchor="center")
        
        # شريط التمرير
        scrollbar_y = ttk.Scrollbar(table_frame, orient="vertical", command=self.contractors_tree.yview)
        scrollbar_x = ttk.Scrollbar(table_frame, orient="horizontal", command=self.contractors_tree.xview)
        
        self.contractors_tree.configure(yscrollcommand=scrollbar_y.set, xscrollcommand=scrollbar_x.set)
        
        # تخطيط الجدول
        self.contractors_tree.pack(side="left", fill="both", expand=True)
        scrollbar_y.pack(side="right", fill="y")
        scrollbar_x.pack(side="bottom", fill="x")
        
        # ربط الأحداث
        self.contractors_tree.bind('<Double-1>', self.edit_contractor)
        
    def setup_contracts_tab(self):
        """إعداد تبويب العقود"""
        # شريط الأدوات
        toolbar_frame = ctk.CTkFrame(self.contracts_tab)
        toolbar_frame.pack(fill="x", padx=10, pady=10)
        
        add_contract_btn = ctk.CTkButton(
            toolbar_frame,
            text="➕ عقد جديد",
            command=self.add_contract,
            width=120,
            height=35,
            fg_color=self.colors['success']
        )
        add_contract_btn.pack(side="right", padx=5, pady=5)
        
        # جدول العقود
        contracts_frame = ctk.CTkFrame(self.contracts_tab)
        contracts_frame.pack(fill="both", expand=True, padx=10, pady=10)
        
        contract_columns = ("رقم العقد", "المقاول", "المشروع", "قيمة العقد", "تاريخ البداية", "تاريخ النهاية", "الحالة")
        
        self.contracts_tree = ttk.Treeview(contracts_frame, columns=contract_columns, show="headings", height=15)
        
        for col in contract_columns:
            self.contracts_tree.heading(col, text=col)
            self.contracts_tree.column(col, width=120, anchor="center")
        
        self.contracts_tree.pack(fill="both", expand=True)
        
    def setup_evaluations_tab(self):
        """إعداد تبويب التقييمات"""
        # شريط الأدوات
        toolbar_frame = ctk.CTkFrame(self.evaluations_tab)
        toolbar_frame.pack(fill="x", padx=10, pady=10)
        
        add_eval_btn = ctk.CTkButton(
            toolbar_frame,
            text="➕ تقييم جديد",
            command=self.add_evaluation,
            width=120,
            height=35,
            fg_color=self.colors['success']
        )
        add_eval_btn.pack(side="right", padx=5, pady=5)
        
        # جدول التقييمات
        evaluations_frame = ctk.CTkFrame(self.evaluations_tab)
        evaluations_frame.pack(fill="both", expand=True, padx=10, pady=10)
        
        eval_columns = ("المقاول", "المشروع", "جودة العمل", "الالتزام بالمواعيد", "التعاون", "التقييم العام", "تاريخ التقييم")
        
        self.evaluations_tree = ttk.Treeview(evaluations_frame, columns=eval_columns, show="headings", height=15)
        
        for col in eval_columns:
            self.evaluations_tree.heading(col, text=col)
            self.evaluations_tree.column(col, width=120, anchor="center")
        
        self.evaluations_tree.pack(fill="both", expand=True)
        
    def setup_payments_tab(self):
        """إعداد تبويب المدفوعات"""
        # شريط الأدوات
        toolbar_frame = ctk.CTkFrame(self.payments_tab)
        toolbar_frame.pack(fill="x", padx=10, pady=10)
        
        add_payment_btn = ctk.CTkButton(
            toolbar_frame,
            text="➕ دفعة جديدة",
            command=self.add_payment,
            width=120,
            height=35,
            fg_color=self.colors['success']
        )
        add_payment_btn.pack(side="right", padx=5, pady=5)
        
        # جدول المدفوعات
        payments_frame = ctk.CTkFrame(self.payments_tab)
        payments_frame.pack(fill="both", expand=True, padx=10, pady=10)
        
        payment_columns = ("المقاول", "العقد", "المبلغ", "تاريخ الدفع", "طريقة الدفع", "الحالة", "ملاحظات")
        
        self.payments_tree = ttk.Treeview(payments_frame, columns=payment_columns, show="headings", height=15)
        
        for col in payment_columns:
            self.payments_tree.heading(col, text=col)
            self.payments_tree.column(col, width=120, anchor="center")
        
        self.payments_tree.pack(fill="both", expand=True)
        
    def load_contractors(self):
        """تحميل قائمة المقاولين"""
        try:
            # مسح البيانات الحالية
            for item in self.contractors_tree.get_children():
                self.contractors_tree.delete(item)
            
            # جلب بيانات المقاولين
            query = """
                SELECT ContractorID, ContractorName, CompanyName, Specialization, 
                       ContactInfo, Email, Rating, IsActive
                FROM Contractors
                ORDER BY ContractorName
            """
            
            contractors = self.db_manager.cursor.execute(query).fetchall()
            
            # إضافة البيانات للجدول
            for contractor in contractors:
                status = "نشط" if contractor[7] else "غير نشط"
                rating = f"{contractor[6]:.1f}/5" if contractor[6] else "غير مقيم"
                
                self.contractors_tree.insert("", "end", values=(
                    contractor[0], contractor[1], contractor[2] or "غير محدد",
                    contractor[3] or "غير محدد", contractor[4] or "",
                    contractor[5] or "", rating, status
                ))
                
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تحميل بيانات المقاولين:\n{str(e)}")
    
    def search_contractors(self, event=None):
        """البحث في المقاولين"""
        search_term = self.search_var.get().lower()
        
        # مسح البيانات الحالية
        for item in self.contractors_tree.get_children():
            self.contractors_tree.delete(item)
        
        if not search_term:
            self.load_contractors()
            return
        
        try:
            query = """
                SELECT ContractorID, ContractorName, CompanyName, Specialization, 
                       ContactInfo, Email, Rating, IsActive
                FROM Contractors
                WHERE LOWER(ContractorName) LIKE ? OR LOWER(CompanyName) LIKE ?
                ORDER BY ContractorName
            """
            
            contractors = self.db_manager.cursor.execute(query, (f'%{search_term}%', f'%{search_term}%')).fetchall()
            
            for contractor in contractors:
                status = "نشط" if contractor[7] else "غير نشط"
                rating = f"{contractor[6]:.1f}/5" if contractor[6] else "غير مقيم"
                
                self.contractors_tree.insert("", "end", values=(
                    contractor[0], contractor[1], contractor[2] or "غير محدد",
                    contractor[3] or "غير محدد", contractor[4] or "",
                    contractor[5] or "", rating, status
                ))
                
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في البحث:\n{str(e)}")
    
    def add_contractor(self):
        """إضافة مقاول جديد"""
        messagebox.showinfo("قيد التطوير", "ميزة إضافة مقاول جديد قيد التطوير")
    
    def edit_contractor(self, event=None):
        """تعديل مقاول"""
        messagebox.showinfo("قيد التطوير", "ميزة تعديل المقاول قيد التطوير")
    
    def add_contract(self):
        """إضافة عقد جديد"""
        messagebox.showinfo("قيد التطوير", "ميزة إضافة عقد جديد قيد التطوير")
    
    def add_evaluation(self):
        """إضافة تقييم جديد"""
        messagebox.showinfo("قيد التطوير", "ميزة إضافة تقييم جديد قيد التطوير")
    
    def add_payment(self):
        """إضافة دفعة جديدة"""
        messagebox.showinfo("قيد التطوير", "ميزة إضافة دفعة جديدة قيد التطوير")
