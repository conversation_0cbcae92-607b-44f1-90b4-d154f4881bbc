-- جداول إضافية لنظام إدارة الموظفين المتقدم
-- Additional tables for Advanced Employee Management System

-- جدول الأقسام (Departments)
CREATE TABLE Departments (
    DepartmentID AUTOINCREMENT PRIMARY KEY,
    DepartmentName TEXT(100) NOT NULL,
    DepartmentCode TEXT(20) UNIQUE,
    Description TEXT(255),
    ManagerID LONG,
    Budget CURRENCY DEFAULT 0,
    IsActive YESNO DEFAULT TRUE,
    CreatedDate DATETIME DEFAULT NOW(),
    FOREIGN KEY (ManagerID) REFERENCES Employees(EmployeeID)
);

-- جدول المناصب (Positions)
CREATE TABLE Positions (
    PositionID AUTOINCREMENT PRIMARY KEY,
    PositionTitle TEXT(100) NOT NULL,
    DepartmentID LONG,
    MinSalary CURRENCY DEFAULT 0,
    MaxSalary CURRENCY DEFAULT 0,
    RequiredSkills TEXT(500),
    JobDescription TEXT(1000),
    IsActive YESNO DEFAULT TRUE,
    CreatedDate DATETIME DEFAULT NOW(),
    FOR<PERSON>G<PERSON> KEY (DepartmentID) REFERENCES Departments(DepartmentID)
);

-- جدول الحضور والانصراف (Attendance)
CREATE TABLE Attendance (
    AttendanceID AUTOINCREMENT PRIMARY KEY,
    EmployeeID LONG NOT NULL,
    AttendanceDate DATE NOT NULL,
    CheckInTime DATETIME,
    CheckOutTime DATETIME,
    WorkingHours SINGLE DEFAULT 0,
    OvertimeHours SINGLE DEFAULT 0,
    Status TEXT(20) DEFAULT 'Present', -- Present, Absent, Late, Sick, Vacation
    Notes TEXT(255),
    CreatedDate DATETIME DEFAULT NOW(),
    FOREIGN KEY (EmployeeID) REFERENCES Employees(EmployeeID)
);

-- جدول الإجازات (Leaves)
CREATE TABLE Leaves (
    LeaveID AUTOINCREMENT PRIMARY KEY,
    EmployeeID LONG NOT NULL,
    LeaveType TEXT(50) NOT NULL, -- Annual, Sick, Emergency, Maternity, etc.
    StartDate DATE NOT NULL,
    EndDate DATE NOT NULL,
    TotalDays LONG NOT NULL,
    Reason TEXT(500),
    Status TEXT(20) DEFAULT 'Pending', -- Pending, Approved, Rejected
    ApprovedBy LONG,
    ApprovalDate DATETIME,
    RequestDate DATETIME DEFAULT NOW(),
    FOREIGN KEY (EmployeeID) REFERENCES Employees(EmployeeID),
    FOREIGN KEY (ApprovedBy) REFERENCES Users(UserID)
);

-- جدول تقييم الأداء (Performance Reviews)
CREATE TABLE PerformanceReviews (
    ReviewID AUTOINCREMENT PRIMARY KEY,
    EmployeeID LONG NOT NULL,
    ReviewPeriodStart DATE NOT NULL,
    ReviewPeriodEnd DATE NOT NULL,
    OverallRating SINGLE DEFAULT 0, -- 1-5 scale
    TechnicalSkills SINGLE DEFAULT 0,
    Communication SINGLE DEFAULT 0,
    Teamwork SINGLE DEFAULT 0,
    Leadership SINGLE DEFAULT 0,
    Punctuality SINGLE DEFAULT 0,
    Goals TEXT(1000),
    Achievements TEXT(1000),
    AreasForImprovement TEXT(1000),
    Comments TEXT(1000),
    ReviewedBy LONG NOT NULL,
    ReviewDate DATETIME DEFAULT NOW(),
    FOREIGN KEY (EmployeeID) REFERENCES Employees(EmployeeID),
    FOREIGN KEY (ReviewedBy) REFERENCES Users(UserID)
);

-- جدول المهارات (Skills)
CREATE TABLE Skills (
    SkillID AUTOINCREMENT PRIMARY KEY,
    SkillName TEXT(100) NOT NULL UNIQUE,
    Category TEXT(50), -- Technical, Soft, Language, etc.
    Description TEXT(255),
    IsActive YESNO DEFAULT TRUE
);

-- جدول مهارات الموظفين (Employee Skills)
CREATE TABLE EmployeeSkills (
    EmployeeSkillID AUTOINCREMENT PRIMARY KEY,
    EmployeeID LONG NOT NULL,
    SkillID LONG NOT NULL,
    ProficiencyLevel TEXT(20) DEFAULT 'Beginner', -- Beginner, Intermediate, Advanced, Expert
    YearsOfExperience SINGLE DEFAULT 0,
    CertificationDate DATE,
    Notes TEXT(255),
    FOREIGN KEY (EmployeeID) REFERENCES Employees(EmployeeID),
    FOREIGN KEY (SkillID) REFERENCES Skills(SkillID)
);

-- جدول التدريب (Training)
CREATE TABLE Training (
    TrainingID AUTOINCREMENT PRIMARY KEY,
    TrainingTitle TEXT(200) NOT NULL,
    Description TEXT(1000),
    TrainingType TEXT(50), -- Internal, External, Online, Workshop
    StartDate DATE,
    EndDate DATE,
    Duration SINGLE DEFAULT 0, -- in hours
    MaxParticipants LONG DEFAULT 0,
    Cost CURRENCY DEFAULT 0,
    TrainerName TEXT(100),
    Location TEXT(255),
    Status TEXT(20) DEFAULT 'Planned', -- Planned, Ongoing, Completed, Cancelled
    CreatedBy LONG,
    CreatedDate DATETIME DEFAULT NOW(),
    FOREIGN KEY (CreatedBy) REFERENCES Users(UserID)
);

-- جدول مشاركة الموظفين في التدريب (Employee Training)
CREATE TABLE EmployeeTraining (
    EmployeeTrainingID AUTOINCREMENT PRIMARY KEY,
    EmployeeID LONG NOT NULL,
    TrainingID LONG NOT NULL,
    RegistrationDate DATETIME DEFAULT NOW(),
    AttendanceStatus TEXT(20) DEFAULT 'Registered', -- Registered, Attended, Absent, Completed
    CompletionDate DATE,
    Grade TEXT(10), -- A, B, C, D, F or percentage
    Certificate YESNO DEFAULT FALSE,
    Feedback TEXT(500),
    FOREIGN KEY (EmployeeID) REFERENCES Employees(EmployeeID),
    FOREIGN KEY (TrainingID) REFERENCES Training(TrainingID)
);

-- جدول الرواتب (Salaries)
CREATE TABLE Salaries (
    SalaryID AUTOINCREMENT PRIMARY KEY,
    EmployeeID LONG NOT NULL,
    BasicSalary CURRENCY NOT NULL,
    Allowances CURRENCY DEFAULT 0,
    Deductions CURRENCY DEFAULT 0,
    NetSalary CURRENCY NOT NULL,
    PayPeriodStart DATE NOT NULL,
    PayPeriodEnd DATE NOT NULL,
    PayDate DATE,
    Status TEXT(20) DEFAULT 'Pending', -- Pending, Paid, Cancelled
    ProcessedBy LONG,
    Notes TEXT(255),
    CreatedDate DATETIME DEFAULT NOW(),
    FOREIGN KEY (EmployeeID) REFERENCES Employees(EmployeeID),
    FOREIGN KEY (ProcessedBy) REFERENCES Users(UserID)
);

-- إدراج بيانات أولية للأقسام
INSERT INTO Departments (DepartmentName, DepartmentCode, Description) VALUES
('الهندسة', 'ENG', 'قسم الهندسة والمشاريع التقنية'),
('الإدارة', 'ADM', 'الإدارة العامة والموارد البشرية'),
('المالية', 'FIN', 'الشؤون المالية والمحاسبة'),
('تقنية المعلومات', 'IT', 'تقنية المعلومات والأنظمة'),
('الخدمات العامة', 'SRV', 'الخدمات العامة والصيانة');

-- إدراج بيانات أولية للمناصب
INSERT INTO Positions (PositionTitle, DepartmentID, MinSalary, MaxSalary, JobDescription) VALUES
('مهندس مشاريع', 1, 800, 1500, 'مسؤول عن تخطيط وتنفيذ المشاريع الهندسية'),
('مدير قسم', 1, 1200, 2000, 'إدارة القسم والإشراف على الموظفين'),
('محاسب', 3, 600, 1000, 'إدارة الحسابات والشؤون المالية'),
('مطور أنظمة', 4, 700, 1300, 'تطوير وصيانة الأنظمة التقنية'),
('موظف خدمات', 5, 400, 700, 'تقديم الخدمات العامة للمواطنين');

-- إدراج بيانات أولية للمهارات
INSERT INTO Skills (SkillName, Category, Description) VALUES
('إدارة المشاريع', 'Technical', 'مهارات تخطيط وتنفيذ المشاريع'),
('AutoCAD', 'Technical', 'برنامج التصميم الهندسي'),
('Microsoft Office', 'Technical', 'حزمة برامج المكتب'),
('التواصل', 'Soft', 'مهارات التواصل والعرض'),
('القيادة', 'Soft', 'مهارات القيادة وإدارة الفرق'),
('اللغة الإنجليزية', 'Language', 'إتقان اللغة الإنجليزية'),
('المحاسبة', 'Technical', 'مهارات المحاسبة والتدقيق'),
('البرمجة', 'Technical', 'مهارات البرمجة وتطوير الأنظمة');
