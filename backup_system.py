#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام النسخ الاحتياطي التلقائي
Automatic Backup System
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import customtkinter as ctk
from datetime import datetime, timedelta
import os
import shutil
import threading
import time
import zipfile
import json
from pathlib import Path

class BackupSystem:
    """نظام النسخ الاحتياطي التلقائي"""
    
    def __init__(self, parent_window, db_manager, current_user):
        self.parent_window = parent_window
        self.db_manager = db_manager
        self.current_user = current_user
        self.window = None
        self.backup_thread = None
        self.is_running = False
        
        # إعداد الألوان
        self.colors = {
            'primary': '#1f538d',
            'secondary': '#2980b9',
            'success': '#27ae60',
            'warning': '#f39c12',
            'danger': '#e74c3c',
            'info': '#3498db',
            'light': '#ecf0f1',
            'dark': '#2c3e50'
        }
        
        # إعداد مجلد النسخ الاحتياطية
        self.backup_dir = Path("backup")
        self.backup_dir.mkdir(exist_ok=True)
        
        # تحميل إعدادات النسخ الاحتياطي
        self.load_backup_settings()
        
        # بدء خدمة النسخ الاحتياطي التلقائي
        self.start_auto_backup_service()
        
    def load_backup_settings(self):
        """تحميل إعدادات النسخ الاحتياطي"""
        settings_file = Path("config/backup_settings.json")
        
        # الإعدادات الافتراضية
        self.settings = {
            'auto_backup_enabled': True,
            'backup_interval_hours': 24,
            'max_backup_files': 30,
            'backup_location': str(self.backup_dir),
            'include_attachments': True,
            'include_reports': True,
            'compress_backups': True
        }
        
        try:
            if settings_file.exists():
                with open(settings_file, 'r', encoding='utf-8') as f:
                    saved_settings = json.load(f)
                    self.settings.update(saved_settings)
        except Exception as e:
            print(f"خطأ في تحميل إعدادات النسخ الاحتياطي: {e}")
            
    def save_backup_settings(self):
        """حفظ إعدادات النسخ الاحتياطي"""
        settings_file = Path("config/backup_settings.json")
        settings_file.parent.mkdir(exist_ok=True)
        
        try:
            with open(settings_file, 'w', encoding='utf-8') as f:
                json.dump(self.settings, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"خطأ في حفظ إعدادات النسخ الاحتياطي: {e}")
            
    def start_auto_backup_service(self):
        """بدء خدمة النسخ الاحتياطي التلقائي"""
        if self.settings['auto_backup_enabled']:
            self.is_running = True
            self.backup_thread = threading.Thread(target=self.auto_backup_worker, daemon=True)
            self.backup_thread.start()
            
    def stop_auto_backup_service(self):
        """إيقاف خدمة النسخ الاحتياطي التلقائي"""
        self.is_running = False
        
    def auto_backup_worker(self):
        """عامل النسخ الاحتياطي التلقائي"""
        while self.is_running:
            try:
                # فحص آخر نسخة احتياطية
                last_backup = self.get_last_backup_time()
                interval_hours = self.settings['backup_interval_hours']
                
                if last_backup is None or (datetime.now() - last_backup).total_seconds() > interval_hours * 3600:
                    print("بدء النسخ الاحتياطي التلقائي...")
                    self.create_backup(auto=True)
                    
                # انتظار ساعة واحدة قبل الفحص التالي
                time.sleep(3600)
                
            except Exception as e:
                print(f"خطأ في خدمة النسخ الاحتياطي التلقائي: {e}")
                time.sleep(3600)
                
    def get_last_backup_time(self):
        """الحصول على وقت آخر نسخة احتياطية"""
        try:
            backup_files = list(self.backup_dir.glob("backup_*.zip"))
            if not backup_files:
                return None
                
            # ترتيب الملفات حسب تاريخ الإنشاء
            backup_files.sort(key=lambda x: x.stat().st_mtime, reverse=True)
            latest_file = backup_files[0]
            
            return datetime.fromtimestamp(latest_file.stat().st_mtime)
            
        except Exception as e:
            print(f"خطأ في الحصول على وقت آخر نسخة احتياطية: {e}")
            return None
            
    def show_backup_management(self):
        """عرض نافذة إدارة النسخ الاحتياطية"""
        self.window = ctk.CTkToplevel(self.parent_window)
        self.window.title("إدارة النسخ الاحتياطية")
        self.window.geometry("900x700")
        self.window.transient(self.parent_window)
        self.window.grab_set()
        
        self.setup_ui()
        self.load_backup_list()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # العنوان الرئيسي
        title_label = ctk.CTkLabel(
            self.window,
            text="إدارة النسخ الاحتياطية",
            font=ctk.CTkFont(size=24, weight="bold")
        )
        title_label.pack(pady=20)
        
        # إطار التبويبات
        self.tabview = ctk.CTkTabview(self.window)
        self.tabview.pack(fill="both", expand=True, padx=20, pady=20)
        
        # تبويب النسخ الاحتياطية
        self.backups_tab = self.tabview.add("النسخ الاحتياطية")
        self.setup_backups_tab()
        
        # تبويب الإعدادات
        self.settings_tab = self.tabview.add("الإعدادات")
        self.setup_settings_tab()
        
        # تبويب الاستعادة
        self.restore_tab = self.tabview.add("الاستعادة")
        self.setup_restore_tab()
        
    def setup_backups_tab(self):
        """إعداد تبويب النسخ الاحتياطية"""
        # شريط الأدوات
        toolbar_frame = ctk.CTkFrame(self.backups_tab)
        toolbar_frame.pack(fill="x", padx=10, pady=10)
        
        # أزرار الأدوات
        create_backup_btn = ctk.CTkButton(
            toolbar_frame,
            text="📦 إنشاء نسخة احتياطية",
            command=self.manual_backup,
            width=150,
            height=35,
            fg_color=self.colors['success']
        )
        create_backup_btn.pack(side="right", padx=5, pady=5)
        
        delete_backup_btn = ctk.CTkButton(
            toolbar_frame,
            text="🗑️ حذف",
            command=self.delete_backup,
            width=100,
            height=35,
            fg_color=self.colors['danger']
        )
        delete_backup_btn.pack(side="right", padx=5, pady=5)
        
        refresh_btn = ctk.CTkButton(
            toolbar_frame,
            text="🔄 تحديث",
            command=self.load_backup_list,
            width=100,
            height=35
        )
        refresh_btn.pack(side="right", padx=5, pady=5)
        
        # معلومات سريعة
        info_frame = ctk.CTkFrame(toolbar_frame, fg_color="transparent")
        info_frame.pack(side="left", padx=5, pady=5)
        
        self.backup_count_label = ctk.CTkLabel(
            info_frame,
            text="عدد النسخ: 0",
            font=ctk.CTkFont(size=12)
        )
        self.backup_count_label.pack(side="left", padx=10)
        
        self.last_backup_label = ctk.CTkLabel(
            info_frame,
            text="آخر نسخة: غير متوفر",
            font=ctk.CTkFont(size=12)
        )
        self.last_backup_label.pack(side="left", padx=10)
        
        # جدول النسخ الاحتياطية
        table_frame = ctk.CTkFrame(self.backups_tab)
        table_frame.pack(fill="both", expand=True, padx=10, pady=10)
        
        # أعمدة الجدول
        columns = ("اسم الملف", "تاريخ الإنشاء", "الحجم", "النوع", "الحالة")
        
        self.backups_tree = ttk.Treeview(table_frame, columns=columns, show="headings", height=15)
        
        # تعريف العناوين
        for col in columns:
            self.backups_tree.heading(col, text=col)
            self.backups_tree.column(col, width=150, anchor="center")
        
        # شريط التمرير
        scrollbar = ttk.Scrollbar(table_frame, orient="vertical", command=self.backups_tree.yview)
        self.backups_tree.configure(yscrollcommand=scrollbar.set)
        
        # تخطيط الجدول
        self.backups_tree.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
        
        # ربط الأحداث
        self.backups_tree.bind('<Double-1>', self.restore_backup)
        
    def setup_settings_tab(self):
        """إعداد تبويب الإعدادات"""
        # إطار الإعدادات العامة
        general_frame = ctk.CTkFrame(self.settings_tab)
        general_frame.pack(fill="x", padx=20, pady=20)
        
        general_title = ctk.CTkLabel(
            general_frame,
            text="الإعدادات العامة",
            font=ctk.CTkFont(size=16, weight="bold")
        )
        general_title.pack(pady=(15, 10))
        
        # تفعيل النسخ التلقائي
        self.auto_backup_var = tk.BooleanVar(value=self.settings['auto_backup_enabled'])
        auto_backup_check = ctk.CTkCheckBox(
            general_frame,
            text="تفعيل النسخ الاحتياطي التلقائي",
            variable=self.auto_backup_var,
            command=self.on_settings_change
        )
        auto_backup_check.pack(anchor="w", padx=20, pady=5)
        
        # فترة النسخ الاحتياطي
        interval_frame = ctk.CTkFrame(general_frame, fg_color="transparent")
        interval_frame.pack(fill="x", padx=20, pady=10)
        
        interval_label = ctk.CTkLabel(interval_frame, text="فترة النسخ الاحتياطي (ساعات):")
        interval_label.pack(side="left")
        
        self.interval_var = tk.StringVar(value=str(self.settings['backup_interval_hours']))
        interval_entry = ctk.CTkEntry(
            interval_frame,
            textvariable=self.interval_var,
            width=100
        )
        interval_entry.pack(side="right")
        interval_entry.bind('<KeyRelease>', self.on_settings_change)
        
        # عدد النسخ المحفوظة
        max_files_frame = ctk.CTkFrame(general_frame, fg_color="transparent")
        max_files_frame.pack(fill="x", padx=20, pady=10)
        
        max_files_label = ctk.CTkLabel(max_files_frame, text="الحد الأقصى للنسخ المحفوظة:")
        max_files_label.pack(side="left")
        
        self.max_files_var = tk.StringVar(value=str(self.settings['max_backup_files']))
        max_files_entry = ctk.CTkEntry(
            max_files_frame,
            textvariable=self.max_files_var,
            width=100
        )
        max_files_entry.pack(side="right")
        max_files_entry.bind('<KeyRelease>', self.on_settings_change)
        
        # مجلد النسخ الاحتياطية
        location_frame = ctk.CTkFrame(general_frame, fg_color="transparent")
        location_frame.pack(fill="x", padx=20, pady=(10, 20))
        
        location_label = ctk.CTkLabel(location_frame, text="مجلد النسخ الاحتياطية:")
        location_label.pack(anchor="w", pady=(0, 5))
        
        location_path_frame = ctk.CTkFrame(location_frame, fg_color="transparent")
        location_path_frame.pack(fill="x")
        
        self.location_var = tk.StringVar(value=self.settings['backup_location'])
        location_entry = ctk.CTkEntry(
            location_path_frame,
            textvariable=self.location_var,
            width=300
        )
        location_entry.pack(side="left", fill="x", expand=True)
        
        browse_btn = ctk.CTkButton(
            location_path_frame,
            text="تصفح",
            command=self.browse_backup_location,
            width=80,
            height=30
        )
        browse_btn.pack(side="right", padx=(5, 0))
        
        # إطار خيارات النسخ
        options_frame = ctk.CTkFrame(self.settings_tab)
        options_frame.pack(fill="x", padx=20, pady=(0, 20))
        
        options_title = ctk.CTkLabel(
            options_frame,
            text="خيارات النسخ",
            font=ctk.CTkFont(size=16, weight="bold")
        )
        options_title.pack(pady=(15, 10))
        
        # تضمين المرفقات
        self.include_attachments_var = tk.BooleanVar(value=self.settings['include_attachments'])
        attachments_check = ctk.CTkCheckBox(
            options_frame,
            text="تضمين المرفقات",
            variable=self.include_attachments_var,
            command=self.on_settings_change
        )
        attachments_check.pack(anchor="w", padx=20, pady=5)
        
        # تضمين التقارير
        self.include_reports_var = tk.BooleanVar(value=self.settings['include_reports'])
        reports_check = ctk.CTkCheckBox(
            options_frame,
            text="تضمين التقارير",
            variable=self.include_reports_var,
            command=self.on_settings_change
        )
        reports_check.pack(anchor="w", padx=20, pady=5)
        
        # ضغط النسخ الاحتياطية
        self.compress_var = tk.BooleanVar(value=self.settings['compress_backups'])
        compress_check = ctk.CTkCheckBox(
            options_frame,
            text="ضغط النسخ الاحتياطية",
            variable=self.compress_var,
            command=self.on_settings_change
        )
        compress_check.pack(anchor="w", padx=20, pady=(5, 20))
        
        # زر حفظ الإعدادات
        save_settings_btn = ctk.CTkButton(
            self.settings_tab,
            text="💾 حفظ الإعدادات",
            command=self.save_settings,
            width=150,
            height=40,
            fg_color=self.colors['success']
        )
        save_settings_btn.pack(pady=20)
        
    def setup_restore_tab(self):
        """إعداد تبويب الاستعادة"""
        # تعليمات الاستعادة
        instructions_frame = ctk.CTkFrame(self.restore_tab)
        instructions_frame.pack(fill="x", padx=20, pady=20)
        
        instructions_title = ctk.CTkLabel(
            instructions_frame,
            text="تعليمات الاستعادة",
            font=ctk.CTkFont(size=16, weight="bold")
        )
        instructions_title.pack(pady=(15, 10))
        
        instructions_text = """
        لاستعادة نسخة احتياطية:
        1. اختر النسخة الاحتياطية من القائمة في تبويب "النسخ الاحتياطية"
        2. انقر نقراً مزدوجاً على النسخة أو استخدم زر "استعادة"
        3. تأكد من إغلاق جميع التطبيقات التي تستخدم قاعدة البيانات
        4. سيتم إنشاء نسخة احتياطية من البيانات الحالية قبل الاستعادة
        5. انتظر حتى اكتمال عملية الاستعادة
        
        تحذير: عملية الاستعادة ستستبدل جميع البيانات الحالية!
        """
        
        instructions_label = ctk.CTkLabel(
            instructions_frame,
            text=instructions_text,
            font=ctk.CTkFont(size=12),
            justify="right"
        )
        instructions_label.pack(padx=20, pady=(0, 20))
        
        # زر استعادة من ملف خارجي
        restore_external_btn = ctk.CTkButton(
            self.restore_tab,
            text="📁 استعادة من ملف خارجي",
            command=self.restore_from_external_file,
            width=200,
            height=40,
            fg_color=self.colors['warning']
        )
        restore_external_btn.pack(pady=20)

    def load_backup_list(self):
        """تحميل قائمة النسخ الاحتياطية"""
        try:
            # مسح البيانات الحالية
            for item in self.backups_tree.get_children():
                self.backups_tree.delete(item)

            # جلب ملفات النسخ الاحتياطية
            backup_files = list(self.backup_dir.glob("backup_*.zip"))
            backup_files.sort(key=lambda x: x.stat().st_mtime, reverse=True)

            # تحديث المعلومات السريعة
            self.backup_count_label.configure(text=f"عدد النسخ: {len(backup_files)}")

            if backup_files:
                latest_file = backup_files[0]
                latest_time = datetime.fromtimestamp(latest_file.stat().st_mtime)
                self.last_backup_label.configure(text=f"آخر نسخة: {latest_time.strftime('%Y/%m/%d %H:%M')}")
            else:
                self.last_backup_label.configure(text="آخر نسخة: غير متوفر")

            # إضافة الملفات للجدول
            for backup_file in backup_files:
                file_name = backup_file.name
                creation_time = datetime.fromtimestamp(backup_file.stat().st_mtime)
                file_size = self.format_file_size(backup_file.stat().st_size)
                backup_type = "تلقائي" if "auto" in file_name else "يدوي"
                status = "مكتمل"

                self.backups_tree.insert("", "end", values=(
                    file_name,
                    creation_time.strftime("%Y/%m/%d %H:%M"),
                    file_size,
                    backup_type,
                    status
                ))

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تحميل قائمة النسخ الاحتياطية:\n{str(e)}")

    def format_file_size(self, size_bytes):
        """تنسيق حجم الملف"""
        if size_bytes == 0:
            return "0 B"

        size_names = ["B", "KB", "MB", "GB"]
        i = 0
        while size_bytes >= 1024 and i < len(size_names) - 1:
            size_bytes /= 1024.0
            i += 1

        return f"{size_bytes:.1f} {size_names[i]}"

    def manual_backup(self):
        """إنشاء نسخة احتياطية يدوية"""
        self.create_backup(auto=False)

    def create_backup(self, auto=False):
        """إنشاء نسخة احتياطية"""
        try:
            # إنشاء اسم الملف
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_type = "auto" if auto else "manual"
            backup_filename = f"backup_{backup_type}_{timestamp}.zip"
            backup_path = self.backup_dir / backup_filename

            if not auto:
                messagebox.showinfo("بدء النسخ الاحتياطي", "بدء إنشاء النسخة الاحتياطية...")

            # إنشاء النسخة الاحتياطية
            with zipfile.ZipFile(backup_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
                # نسخ قاعدة البيانات
                db_path = Path("database/project_management.accdb")
                if db_path.exists():
                    zipf.write(db_path, "database/project_management.accdb")

                # نسخ ملفات الإعدادات
                config_dir = Path("config")
                if config_dir.exists():
                    for config_file in config_dir.rglob("*"):
                        if config_file.is_file():
                            zipf.write(config_file, config_file)

                # نسخ المرفقات إذا كان مطلوباً
                if self.settings['include_attachments']:
                    attachments_dir = Path("attachments")
                    if attachments_dir.exists():
                        for attachment in attachments_dir.rglob("*"):
                            if attachment.is_file():
                                zipf.write(attachment, attachment)

                # نسخ التقارير إذا كان مطلوباً
                if self.settings['include_reports']:
                    reports_dir = Path("reports")
                    if reports_dir.exists():
                        for report in reports_dir.rglob("*"):
                            if report.is_file():
                                zipf.write(report, report)

                # إضافة معلومات النسخة الاحتياطية
                backup_info = {
                    'creation_date': datetime.now().isoformat(),
                    'created_by': self.current_user['full_name'],
                    'backup_type': backup_type,
                    'version': '1.1.0',
                    'includes_attachments': self.settings['include_attachments'],
                    'includes_reports': self.settings['include_reports']
                }

                zipf.writestr("backup_info.json", json.dumps(backup_info, ensure_ascii=False, indent=2))

            # تنظيف النسخ القديمة
            self.cleanup_old_backups()

            if not auto:
                messagebox.showinfo("نجح", f"تم إنشاء النسخة الاحتياطية بنجاح:\n{backup_filename}")
                self.load_backup_list()
            else:
                print(f"تم إنشاء النسخة الاحتياطية التلقائية: {backup_filename}")

        except Exception as e:
            error_msg = f"فشل في إنشاء النسخة الاحتياطية:\n{str(e)}"
            if not auto:
                messagebox.showerror("خطأ", error_msg)
            else:
                print(error_msg)

    def cleanup_old_backups(self):
        """تنظيف النسخ الاحتياطية القديمة"""
        try:
            backup_files = list(self.backup_dir.glob("backup_*.zip"))
            backup_files.sort(key=lambda x: x.stat().st_mtime, reverse=True)

            max_files = self.settings['max_backup_files']
            if len(backup_files) > max_files:
                files_to_delete = backup_files[max_files:]
                for file_to_delete in files_to_delete:
                    file_to_delete.unlink()
                    print(f"تم حذف النسخة الاحتياطية القديمة: {file_to_delete.name}")

        except Exception as e:
            print(f"خطأ في تنظيف النسخ القديمة: {e}")

    def delete_backup(self):
        """حذف نسخة احتياطية"""
        selected = self.backups_tree.selection()
        if not selected:
            messagebox.showwarning("تحذير", "يرجى اختيار نسخة احتياطية للحذف")
            return

        item = self.backups_tree.item(selected[0])
        filename = item['values'][0]

        result = messagebox.askyesno(
            "تأكيد الحذف",
            f"هل أنت متأكد من حذف النسخة الاحتياطية:\n{filename}؟"
        )

        if result:
            try:
                backup_path = self.backup_dir / filename
                backup_path.unlink()
                messagebox.showinfo("نجح", "تم حذف النسخة الاحتياطية بنجاح")
                self.load_backup_list()
            except Exception as e:
                messagebox.showerror("خطأ", f"فشل في حذف النسخة الاحتياطية:\n{str(e)}")

    def restore_backup(self, event=None):
        """استعادة نسخة احتياطية"""
        selected = self.backups_tree.selection()
        if not selected:
            messagebox.showwarning("تحذير", "يرجى اختيار نسخة احتياطية للاستعادة")
            return

        item = self.backups_tree.item(selected[0])
        filename = item['values'][0]

        result = messagebox.askyesno(
            "تأكيد الاستعادة",
            f"هل أنت متأكد من استعادة النسخة الاحتياطية:\n{filename}؟\n\nتحذير: سيتم استبدال جميع البيانات الحالية!"
        )

        if result:
            try:
                # إنشاء نسخة احتياطية من البيانات الحالية قبل الاستعادة
                self.create_backup(auto=False)

                # استعادة النسخة الاحتياطية
                backup_path = self.backup_dir / filename

                with zipfile.ZipFile(backup_path, 'r') as zipf:
                    zipf.extractall(Path.cwd())

                messagebox.showinfo("نجح", "تم استعادة النسخة الاحتياطية بنجاح!\nيرجى إعادة تشغيل البرنامج.")

            except Exception as e:
                messagebox.showerror("خطأ", f"فشل في استعادة النسخة الاحتياطية:\n{str(e)}")

    def restore_from_external_file(self):
        """استعادة من ملف خارجي"""
        file_path = filedialog.askopenfilename(
            title="اختر ملف النسخة الاحتياطية",
            filetypes=[("ملفات النسخ الاحتياطية", "*.zip"), ("جميع الملفات", "*.*")]
        )

        if file_path:
            result = messagebox.askyesno(
                "تأكيد الاستعادة",
                f"هل أنت متأكد من استعادة النسخة الاحتياطية من:\n{file_path}؟\n\nتحذير: سيتم استبدال جميع البيانات الحالية!"
            )

            if result:
                try:
                    # إنشاء نسخة احتياطية من البيانات الحالية
                    self.create_backup(auto=False)

                    # استعادة النسخة الاحتياطية
                    with zipfile.ZipFile(file_path, 'r') as zipf:
                        zipf.extractall(Path.cwd())

                    messagebox.showinfo("نجح", "تم استعادة النسخة الاحتياطية بنجاح!\nيرجى إعادة تشغيل البرنامج.")

                except Exception as e:
                    messagebox.showerror("خطأ", f"فشل في استعادة النسخة الاحتياطية:\n{str(e)}")

    def on_settings_change(self, event=None):
        """معالج تغيير الإعدادات"""
        # تحديث الإعدادات من واجهة المستخدم
        try:
            self.settings['auto_backup_enabled'] = self.auto_backup_var.get()
            self.settings['backup_interval_hours'] = int(self.interval_var.get())
            self.settings['max_backup_files'] = int(self.max_files_var.get())
            self.settings['backup_location'] = self.location_var.get()
            self.settings['include_attachments'] = self.include_attachments_var.get()
            self.settings['include_reports'] = self.include_reports_var.get()
            self.settings['compress_backups'] = self.compress_var.get()
        except ValueError:
            pass  # تجاهل الأخطاء في التحويل

    def browse_backup_location(self):
        """تصفح مجلد النسخ الاحتياطية"""
        folder_path = filedialog.askdirectory(
            title="اختر مجلد النسخ الاحتياطية",
            initialdir=self.settings['backup_location']
        )

        if folder_path:
            self.location_var.set(folder_path)
            self.on_settings_change()

    def save_settings(self):
        """حفظ الإعدادات"""
        try:
            self.on_settings_change()
            self.save_backup_settings()

            # إعادة تشغيل خدمة النسخ الاحتياطي إذا لزم الأمر
            if self.settings['auto_backup_enabled'] and not self.is_running:
                self.start_auto_backup_service()
            elif not self.settings['auto_backup_enabled'] and self.is_running:
                self.stop_auto_backup_service()

            messagebox.showinfo("نجح", "تم حفظ الإعدادات بنجاح")

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في حفظ الإعدادات:\n{str(e)}")
