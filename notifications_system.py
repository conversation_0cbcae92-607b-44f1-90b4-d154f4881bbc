#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام الإشعارات والتنبيهات
Notifications and Alerts System
"""

import tkinter as tk
from tkinter import ttk, messagebox
import customtkinter as ctk
from datetime import datetime, date, timedelta
import threading
import time

class NotificationSystem:
    """نظام الإشعارات والتنبيهات"""
    
    def __init__(self, parent_window, db_manager, current_user):
        self.parent_window = parent_window
        self.db_manager = db_manager
        self.current_user = current_user
        self.window = None
        self.notifications = []
        self.is_running = False
        
        # إعداد الألوان
        self.colors = {
            'primary': '#1f538d',
            'secondary': '#2980b9',
            'success': '#27ae60',
            'warning': '#f39c12',
            'danger': '#e74c3c',
            'info': '#3498db',
            'light': '#ecf0f1',
            'dark': '#2c3e50'
        }
        
        # بدء خدمة الإشعارات
        self.start_notification_service()
        
    def start_notification_service(self):
        """بدء خدمة الإشعارات في الخلفية"""
        self.is_running = True
        self.notification_thread = threading.Thread(target=self.notification_worker, daemon=True)
        self.notification_thread.start()
        
    def stop_notification_service(self):
        """إيقاف خدمة الإشعارات"""
        self.is_running = False
        
    def notification_worker(self):
        """عامل الإشعارات في الخلفية"""
        while self.is_running:
            try:
                self.check_notifications()
                time.sleep(300)  # فحص كل 5 دقائق
            except Exception as e:
                print(f"خطأ في خدمة الإشعارات: {e}")
                time.sleep(60)  # انتظار دقيقة في حالة الخطأ
                
    def check_notifications(self):
        """فحص الإشعارات الجديدة"""
        try:
            # فحص المشاريع المتأخرة
            self.check_overdue_projects()
            
            # فحص المهام المتأخرة
            self.check_overdue_tasks()
            
            # فحص المواعيد النهائية القريبة
            self.check_upcoming_deadlines()
            
            # فحص المخزون المنخفض
            self.check_low_inventory()
            
        except Exception as e:
            print(f"خطأ في فحص الإشعارات: {e}")
            
    def check_overdue_projects(self):
        """فحص المشاريع المتأخرة"""
        try:
            query = """
                SELECT ProjectID, ProjectName, EndDate
                FROM Projects
                WHERE Status = 'InProgress' AND EndDate < ?
            """
            
            overdue_projects = self.db_manager.cursor.execute(query, (datetime.now().date(),)).fetchall()
            
            for project in overdue_projects:
                notification = {
                    'type': 'warning',
                    'title': 'مشروع متأخر',
                    'message': f'المشروع "{project[1]}" متأخر عن الموعد المحدد ({project[2]})',
                    'timestamp': datetime.now(),
                    'read': False,
                    'project_id': project[0]
                }
                self.add_notification(notification)
                
        except Exception as e:
            print(f"خطأ في فحص المشاريع المتأخرة: {e}")
            
    def check_overdue_tasks(self):
        """فحص المهام المتأخرة"""
        try:
            query = """
                SELECT t.TaskID, t.TaskName, t.EndDate, p.ProjectName
                FROM Tasks t
                LEFT JOIN Projects p ON t.ProjectID = p.ProjectID
                WHERE t.Status IN ('NotStarted', 'InProgress') AND t.EndDate < ?
            """
            
            overdue_tasks = self.db_manager.cursor.execute(query, (datetime.now().date(),)).fetchall()
            
            for task in overdue_tasks:
                notification = {
                    'type': 'danger',
                    'title': 'مهمة متأخرة',
                    'message': f'المهمة "{task[1]}" في مشروع "{task[3]}" متأخرة عن الموعد ({task[2]})',
                    'timestamp': datetime.now(),
                    'read': False,
                    'task_id': task[0]
                }
                self.add_notification(notification)
                
        except Exception as e:
            print(f"خطأ في فحص المهام المتأخرة: {e}")
            
    def check_upcoming_deadlines(self):
        """فحص المواعيد النهائية القريبة"""
        try:
            # فحص المشاريع التي تنتهي خلال 7 أيام
            upcoming_date = datetime.now().date() + timedelta(days=7)
            
            query = """
                SELECT ProjectID, ProjectName, EndDate
                FROM Projects
                WHERE Status = 'InProgress' AND EndDate BETWEEN ? AND ?
            """
            
            upcoming_projects = self.db_manager.cursor.execute(
                query, (datetime.now().date(), upcoming_date)
            ).fetchall()
            
            for project in upcoming_projects:
                days_left = (project[2] - datetime.now().date()).days
                notification = {
                    'type': 'info',
                    'title': 'موعد نهائي قريب',
                    'message': f'المشروع "{project[1]}" ينتهي خلال {days_left} أيام ({project[2]})',
                    'timestamp': datetime.now(),
                    'read': False,
                    'project_id': project[0]
                }
                self.add_notification(notification)
                
        except Exception as e:
            print(f"خطأ في فحص المواعيد القريبة: {e}")
            
    def check_low_inventory(self):
        """فحص المخزون المنخفض"""
        # هذه دالة وهمية لأن جدول المخزون غير موجود حالياً
        try:
            # بيانات وهمية للمخزون المنخفض
            low_stock_items = [
                ("حديد تسليح", 5, 10),
                ("أسمنت", 45, 50)
            ]
            
            for item_name, current_qty, min_qty in low_stock_items:
                if current_qty <= min_qty:
                    notification = {
                        'type': 'warning',
                        'title': 'مخزون منخفض',
                        'message': f'المادة "{item_name}" وصلت للحد الأدنى (متوفر: {current_qty}, الحد الأدنى: {min_qty})',
                        'timestamp': datetime.now(),
                        'read': False,
                        'item_name': item_name
                    }
                    self.add_notification(notification)
                    
        except Exception as e:
            print(f"خطأ في فحص المخزون: {e}")
            
    def add_notification(self, notification):
        """إضافة إشعار جديد"""
        # تجنب الإشعارات المكررة
        existing = any(
            n['title'] == notification['title'] and 
            n['message'] == notification['message'] and
            (datetime.now() - n['timestamp']).seconds < 3600  # خلال ساعة واحدة
            for n in self.notifications
        )
        
        if not existing:
            self.notifications.append(notification)
            self.save_notification_to_db(notification)
            
            # عرض إشعار فوري للمستخدم
            if hasattr(self, 'parent_window') and self.parent_window:
                self.show_popup_notification(notification)
                
    def save_notification_to_db(self, notification):
        """حفظ الإشعار في قاعدة البيانات"""
        try:
            query = """
                INSERT INTO Notifications (UserID, Title, Message, NotificationType, IsRead, CreatedDate)
                VALUES (?, ?, ?, ?, ?, ?)
            """
            
            self.db_manager.cursor.execute(query, (
                self.current_user['user_id'],
                notification['title'],
                notification['message'],
                notification['type'],
                notification['read'],
                notification['timestamp']
            ))
            self.db_manager.connection.commit()
            
        except Exception as e:
            print(f"خطأ في حفظ الإشعار: {e}")
            
    def show_popup_notification(self, notification):
        """عرض إشعار منبثق"""
        try:
            # إنشاء نافذة إشعار صغيرة
            popup = tk.Toplevel(self.parent_window)
            popup.title("إشعار جديد")
            popup.geometry("350x150")
            popup.resizable(False, False)
            
            # تحديد لون الخلفية حسب نوع الإشعار
            bg_color = {
                'info': '#e3f2fd',
                'warning': '#fff3e0',
                'danger': '#ffebee',
                'success': '#e8f5e8'
            }.get(notification['type'], '#f5f5f5')
            
            popup.configure(bg=bg_color)
            
            # أيقونة الإشعار
            icon = {
                'info': 'ℹ️',
                'warning': '⚠️',
                'danger': '🚨',
                'success': '✅'
            }.get(notification['type'], '📢')
            
            # العنوان
            title_label = tk.Label(
                popup,
                text=f"{icon} {notification['title']}",
                font=('Arial', 12, 'bold'),
                bg=bg_color,
                fg='#333'
            )
            title_label.pack(pady=10)
            
            # الرسالة
            message_label = tk.Label(
                popup,
                text=notification['message'],
                font=('Arial', 10),
                bg=bg_color,
                fg='#555',
                wraplength=300,
                justify='center'
            )
            message_label.pack(pady=5)
            
            # زر الإغلاق
            close_btn = tk.Button(
                popup,
                text="إغلاق",
                command=popup.destroy,
                bg='#2196F3',
                fg='white',
                font=('Arial', 10),
                relief='flat',
                padx=20
            )
            close_btn.pack(pady=10)
            
            # وضع النافذة في المقدمة
            popup.transient(self.parent_window)
            popup.lift()
            popup.focus_force()
            
            # إغلاق تلقائي بعد 10 ثوان
            popup.after(10000, popup.destroy)
            
        except Exception as e:
            print(f"خطأ في عرض الإشعار المنبثق: {e}")
            
    def show_notifications_window(self):
        """عرض نافذة الإشعارات"""
        self.window = ctk.CTkToplevel(self.parent_window)
        self.window.title("الإشعارات والتنبيهات")
        self.window.geometry("800x600")
        self.window.transient(self.parent_window)
        self.window.grab_set()
        
        self.setup_notifications_ui()
        self.load_notifications()
        
    def setup_notifications_ui(self):
        """إعداد واجهة الإشعارات"""
        # العنوان الرئيسي
        title_label = ctk.CTkLabel(
            self.window,
            text="الإشعارات والتنبيهات",
            font=ctk.CTkFont(size=24, weight="bold")
        )
        title_label.pack(pady=20)
        
        # شريط الأدوات
        toolbar_frame = ctk.CTkFrame(self.window)
        toolbar_frame.pack(fill="x", padx=20, pady=10)
        
        # زر تحديث
        refresh_btn = ctk.CTkButton(
            toolbar_frame,
            text="🔄 تحديث",
            command=self.load_notifications,
            width=100,
            height=35
        )
        refresh_btn.pack(side="right", padx=5, pady=5)
        
        # زر تعليم الكل كمقروء
        mark_all_btn = ctk.CTkButton(
            toolbar_frame,
            text="✅ تعليم الكل كمقروء",
            command=self.mark_all_as_read,
            width=150,
            height=35,
            fg_color=self.colors['success']
        )
        mark_all_btn.pack(side="right", padx=5, pady=5)
        
        # جدول الإشعارات
        table_frame = ctk.CTkFrame(self.window)
        table_frame.pack(fill="both", expand=True, padx=20, pady=20)
        
        # أعمدة الجدول
        columns = ("النوع", "العنوان", "الرسالة", "التاريخ", "الحالة")
        
        self.notifications_tree = ttk.Treeview(table_frame, columns=columns, show="headings", height=20)
        
        # تعريف العناوين
        for col in columns:
            self.notifications_tree.heading(col, text=col)
            self.notifications_tree.column(col, width=150, anchor="center")
        
        # شريط التمرير
        scrollbar = ttk.Scrollbar(table_frame, orient="vertical", command=self.notifications_tree.yview)
        self.notifications_tree.configure(yscrollcommand=scrollbar.set)
        
        # تخطيط الجدول
        self.notifications_tree.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
        
        # ربط الأحداث
        self.notifications_tree.bind('<Double-1>', self.mark_notification_as_read)
        
    def load_notifications(self):
        """تحميل الإشعارات"""
        try:
            # مسح البيانات الحالية
            for item in self.notifications_tree.get_children():
                self.notifications_tree.delete(item)
            
            # جلب الإشعارات من قاعدة البيانات
            query = """
                SELECT NotificationType, Title, Message, CreatedDate, IsRead
                FROM Notifications
                WHERE UserID = ?
                ORDER BY CreatedDate DESC
                LIMIT 100
            """
            
            notifications = self.db_manager.cursor.execute(query, (self.current_user['user_id'],)).fetchall()
            
            # إضافة البيانات للجدول
            for notification in notifications:
                # تحديد الأيقونة حسب النوع
                icon = {
                    'info': 'ℹ️',
                    'warning': '⚠️',
                    'danger': '🚨',
                    'success': '✅'
                }.get(notification[0], '📢')
                
                status = "مقروء" if notification[4] else "غير مقروء"
                date_str = notification[3].strftime("%Y/%m/%d %H:%M") if notification[3] else ""
                
                item = self.notifications_tree.insert("", "end", values=(
                    f"{icon} {notification[0]}",
                    notification[1],
                    notification[2][:50] + "..." if len(notification[2]) > 50 else notification[2],
                    date_str,
                    status
                ))
                
                # تلوين الإشعارات غير المقروءة
                if not notification[4]:
                    self.notifications_tree.set(item, "الحالة", "🔴 غير مقروء")
                    
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تحميل الإشعارات:\n{str(e)}")
            
    def mark_notification_as_read(self, event=None):
        """تعليم إشعار كمقروء"""
        messagebox.showinfo("قيد التطوير", "ميزة تعليم الإشعار كمقروء قيد التطوير")
        
    def mark_all_as_read(self):
        """تعليم جميع الإشعارات كمقروءة"""
        try:
            query = """
                UPDATE Notifications 
                SET IsRead = 1, ReadDate = ?
                WHERE UserID = ? AND IsRead = 0
            """
            
            self.db_manager.cursor.execute(query, (datetime.now(), self.current_user['user_id']))
            self.db_manager.connection.commit()
            
            messagebox.showinfo("نجح", "تم تعليم جميع الإشعارات كمقروءة")
            self.load_notifications()
            
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تحديث الإشعارات:\n{str(e)}")
            
    def get_unread_count(self):
        """الحصول على عدد الإشعارات غير المقروءة"""
        try:
            query = """
                SELECT COUNT(*) FROM Notifications 
                WHERE UserID = ? AND IsRead = 0
            """
            
            count = self.db_manager.cursor.execute(query, (self.current_user['user_id'],)).fetchone()[0]
            return count
            
        except Exception as e:
            print(f"خطأ في جلب عدد الإشعارات: {e}")
            return 0
