#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مولدات التقارير لنظام إدارة المشاريع الهندسية
Report Generators for Engineering Project Management System
"""

import os
from pathlib import Path
from datetime import datetime

# Optional matplotlib import
try:
    import matplotlib.pyplot as plt
    import matplotlib.dates as mdates
    MATPLOTLIB_AVAILABLE = True
except ImportError:
    MATPLOTLIB_AVAILABLE = False
    print("تحذير: matplotlib غير متوفر - سيتم تعطيل الرسوم البيانية في التقارير")
    print("Warning: matplotlib not available - charts in reports will be disabled")

# Optional reportlab import
try:
    from reportlab.lib.pagesizes import A4
    from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle, Image
    from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
    from reportlab.lib.units import inch
    from reportlab.lib import colors
    from reportlab.pdfbase import pdfmetrics
    from reportlab.pdfbase.ttfonts import TTFont
    REPORTLAB_AVAILABLE = True
except ImportError:
    REPORTLAB_AVAILABLE = False
    print("تحذير: reportlab غير متوفر - سيتم تعطيل تقارير PDF")
    print("Warning: reportlab not available - PDF reports will be disabled")
import openpyxl
from openpyxl.styles import Font, Alignment, PatternFill, Border, Side
from openpyxl.chart import BarChart, Reference

# Optional python-docx import
try:
    from docx import Document
    from docx.shared import Inches
    from docx.enum.text import WD_ALIGN_PARAGRAPH
    DOCX_AVAILABLE = True
except ImportError:
    DOCX_AVAILABLE = False
    print("تحذير: python-docx غير متوفر - سيتم تعطيل تقارير Word")
    print("Warning: python-docx not available - Word reports will be disabled")

class BaseReportGenerator:
    """الفئة الأساسية لمولدات التقارير"""
    
    def __init__(self, db_manager):
        """تهيئة المولد"""
        self.db_manager = db_manager
        self.reports_dir = Path("reports")
        self.reports_dir.mkdir(exist_ok=True)
        
        # إعداد الخطوط العربية
        self.setup_arabic_fonts()
    
    def setup_arabic_fonts(self):
        """إعداد الخطوط العربية"""
        try:
            # محاولة تسجيل خط عربي (يجب وضع ملف الخط في مجلد fonts)
            font_path = Path("fonts/NotoSansArabic-Regular.ttf")
            if font_path.exists():
                pdfmetrics.registerFont(TTFont('Arabic', str(font_path)))
        except:
            pass
    
    def get_projects_data(self, start_date, end_date, project_filter=None):
        """الحصول على بيانات المشاريع"""
        query = """
            SELECT p.ProjectID, p.ProjectName, p.ProjectType, p.Location, p.StartDate, p.EndDate,
                   p.PlannedBudget, p.ActualBudget, e.FullName as ProjectManager, p.Status, 
                   p.CompletionPercentage, p.Priority
            FROM Projects p
            LEFT JOIN Employees e ON p.ProjectManager = e.EmployeeID
            WHERE p.CreatedDate BETWEEN ? AND ?
        """
        
        params = [start_date, end_date]
        
        if project_filter and project_filter != "جميع المشاريع":
            query += " AND p.ProjectID = ?"
            # استخراج ID من النص
            project_id = project_filter.split("ID: ")[1].split(")")[0]
            params.append(project_id)
        
        query += " ORDER BY p.CreatedDate DESC"
        
        return self.db_manager.execute_query(query, params)
    
    def get_statistics(self, start_date, end_date):
        """الحصول على الإحصائيات"""
        stats = {}
        
        # إجمالي المشاريع في الفترة
        query = "SELECT COUNT(*) FROM Projects WHERE CreatedDate BETWEEN ? AND ?"
        result = self.db_manager.execute_query(query, (start_date, end_date))
        stats['total_projects'] = result[0][0] if result else 0
        
        # المشاريع المكتملة
        query = "SELECT COUNT(*) FROM Projects WHERE Status = 'Completed' AND CreatedDate BETWEEN ? AND ?"
        result = self.db_manager.execute_query(query, (start_date, end_date))
        stats['completed_projects'] = result[0][0] if result else 0
        
        # المشاريع النشطة
        query = "SELECT COUNT(*) FROM Projects WHERE Status = 'InProgress' AND CreatedDate BETWEEN ? AND ?"
        result = self.db_manager.execute_query(query, (start_date, end_date))
        stats['active_projects'] = result[0][0] if result else 0
        
        # إجمالي الميزانية
        query = "SELECT SUM(PlannedBudget), SUM(ActualBudget) FROM Projects WHERE CreatedDate BETWEEN ? AND ?"
        result = self.db_manager.execute_query(query, (start_date, end_date))
        if result and result[0][0]:
            stats['total_planned_budget'] = result[0][0]
            stats['total_actual_budget'] = result[0][1] or 0
        else:
            stats['total_planned_budget'] = 0
            stats['total_actual_budget'] = 0
        
        return stats
    
    def create_chart(self, data, chart_type, title, filename):
        """إنشاء رسم بياني"""
        if not MATPLOTLIB_AVAILABLE:
            print("تحذير: matplotlib غير متوفر - لا يمكن إنشاء الرسوم البيانية")
            return None

        plt.figure(figsize=(10, 6))
        plt.rcParams['font.family'] = ['Arial Unicode MS', 'Tahoma', 'DejaVu Sans']

        if chart_type == 'bar':
            labels, values = zip(*data) if data else ([], [])
            plt.bar(labels, values, color=['#3498db', '#e74c3c', '#f39c12', '#27ae60'])
            plt.xticks(rotation=45)
        elif chart_type == 'pie':
            labels, values = zip(*data) if data else ([], [])
            plt.pie(values, labels=labels, autopct='%1.1f%%', startangle=90)

        plt.title(title, fontsize=14, fontweight='bold')
        plt.tight_layout()

        chart_path = self.reports_dir / filename
        plt.savefig(chart_path, dpi=300, bbox_inches='tight')
        plt.close()

        return str(chart_path)

class PDFReportGenerator(BaseReportGenerator):
    """مولد تقارير PDF"""
    
    def generate_report(self, report_type, data, options):
        """إنشاء تقرير PDF"""
        if not REPORTLAB_AVAILABLE:
            print("تحذير: reportlab غير متوفر - لا يمكن إنشاء تقارير PDF")
            return None

        try:
            # إنشاء اسم الملف
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"{report_type}_{timestamp}.pdf"
            file_path = self.reports_dir / filename

            # إنشاء المستند
            doc = SimpleDocTemplate(str(file_path), pagesize=A4)
            story = []
            
            # الأنماط
            styles = getSampleStyleSheet()
            title_style = ParagraphStyle(
                'CustomTitle',
                parent=styles['Heading1'],
                fontSize=18,
                spaceAfter=30,
                alignment=1,  # توسيط
                textColor=colors.darkblue
            )
            
            # العنوان
            title = self.get_report_title(report_type)
            story.append(Paragraph(title, title_style))
            story.append(Spacer(1, 20))
            
            # معلومات التقرير
            info_data = [
                ['تاريخ الإنشاء:', data['generation_date'].strftime("%Y/%m/%d %H:%M")],
                ['المنشئ:', data['generated_by']],
                ['الفترة:', f"من {data['start_date']} إلى {data['end_date']}"],
                ['المشاريع:', data['projects']]
            ]
            
            info_table = Table(info_data, colWidths=[2*inch, 3*inch])
            info_table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (0, -1), colors.lightgrey),
                ('TEXTCOLOR', (0, 0), (-1, -1), colors.black),
                ('ALIGN', (0, 0), (-1, -1), 'RIGHT'),
                ('FONTNAME', (0, 0), (-1, -1), 'Helvetica'),
                ('FONTSIZE', (0, 0), (-1, -1), 10),
                ('BOTTOMPADDING', (0, 0), (-1, -1), 12),
                ('BACKGROUND', (1, 0), (1, -1), colors.beige),
                ('GRID', (0, 0), (-1, -1), 1, colors.black)
            ]))
            
            story.append(info_table)
            story.append(Spacer(1, 30))
            
            # المحتوى حسب نوع التقرير
            if report_type == 'project_summary':
                self.add_project_summary_content(story, data, options)
            elif report_type in ['daily_progress', 'weekly_progress', 'monthly_progress']:
                self.add_progress_content(story, data, options)
            elif report_type == 'financial_report':
                self.add_financial_content(story, data, options)
            
            # بناء المستند
            doc.build(story)
            
            return str(file_path)
            
        except Exception as e:
            print(f"خطأ في إنشاء تقرير PDF: {e}")
            return None
    
    def get_report_title(self, report_type):
        """الحصول على عنوان التقرير"""
        titles = {
            'project_summary': 'تقرير ملخص المشاريع الهندسية',
            'daily_progress': 'تقرير التقدم اليومي',
            'weekly_progress': 'تقرير التقدم الأسبوعي',
            'monthly_progress': 'تقرير التقدم الشهري',
            'financial_report': 'التقرير المالي',
            'employees_report': 'تقرير الموظفين'
        }
        return titles.get(report_type, 'تقرير المشاريع الهندسية')
    
    def add_project_summary_content(self, story, data, options):
        """إضافة محتوى ملخص المشاريع"""
        # الحصول على البيانات
        projects_data = self.get_projects_data(data['start_date'], data['end_date'], data['projects'])
        stats = self.get_statistics(data['start_date'], data['end_date'])
        
        # الإحصائيات
        styles = getSampleStyleSheet()
        story.append(Paragraph("الإحصائيات العامة", styles['Heading2']))
        
        stats_data = [
            ['إجمالي المشاريع', str(stats['total_projects'])],
            ['المشاريع المكتملة', str(stats['completed_projects'])],
            ['المشاريع النشطة', str(stats['active_projects'])],
            ['إجمالي الميزانية المخططة', f"{stats['total_planned_budget']:,.0f} ريال"],
            ['إجمالي الميزانية الفعلية', f"{stats['total_actual_budget']:,.0f} ريال"]
        ]
        
        stats_table = Table(stats_data, colWidths=[3*inch, 2*inch])
        stats_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (0, -1), colors.lightblue),
            ('TEXTCOLOR', (0, 0), (-1, -1), colors.black),
            ('ALIGN', (0, 0), (-1, -1), 'RIGHT'),
            ('FONTNAME', (0, 0), (-1, -1), 'Helvetica'),
            ('FONTSIZE', (0, 0), (-1, -1), 10),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 12),
            ('GRID', (0, 0), (-1, -1), 1, colors.black)
        ]))
        
        story.append(stats_table)
        story.append(Spacer(1, 30))
        
        # جدول المشاريع
        if projects_data:
            story.append(Paragraph("تفاصيل المشاريع", styles['Heading2']))
            
            # إعداد بيانات الجدول
            table_data = [['اسم المشروع', 'النوع', 'الحالة', 'الميزانية', 'نسبة الإنجاز']]
            
            for project in projects_data:
                table_data.append([
                    project[1][:30] + "..." if len(project[1]) > 30 else project[1],  # اسم المشروع
                    project[2] or "",  # النوع
                    self.get_status_arabic(project[9]),  # الحالة
                    f"{project[6]:,.0f}" if project[6] else "0",  # الميزانية
                    f"{project[10]:.1f}%" if project[10] else "0%"  # نسبة الإنجاز
                ])
            
            projects_table = Table(table_data, colWidths=[2.5*inch, 1*inch, 1*inch, 1*inch, 1*inch])
            projects_table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
                ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                ('FONTSIZE', (0, 0), (-1, 0), 10),
                ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
                ('GRID', (0, 0), (-1, -1), 1, colors.black)
            ]))
            
            story.append(projects_table)
        
        # إضافة الرسوم البيانية إذا كانت مطلوبة
        if options.get('include_charts', False):
            self.add_charts_to_story(story, stats)
    
    def add_progress_content(self, story, data, options):
        """إضافة محتوى تقرير التقدم"""
        # سيتم تطوير هذا المحتوى
        styles = getSampleStyleSheet()
        story.append(Paragraph("تقرير التقدم قيد التطوير", styles['Normal']))
    
    def add_financial_content(self, story, data, options):
        """إضافة المحتوى المالي"""
        # سيتم تطوير هذا المحتوى
        styles = getSampleStyleSheet()
        story.append(Paragraph("التقرير المالي قيد التطوير", styles['Normal']))
    
    def add_charts_to_story(self, story, stats):
        """إضافة الرسوم البيانية للتقرير"""
        try:
            # رسم بياني للحالات
            chart_data = [
                ('مكتملة', stats['completed_projects']),
                ('نشطة', stats['active_projects']),
                ('أخرى', stats['total_projects'] - stats['completed_projects'] - stats['active_projects'])
            ]
            
            chart_path = self.create_chart(chart_data, 'pie', 'توزيع المشاريع حسب الحالة', 'projects_status_chart.png')
            
            if os.path.exists(chart_path):
                story.append(Spacer(1, 20))
                story.append(Image(chart_path, width=4*inch, height=3*inch))
        except:
            pass
    
    def get_status_arabic(self, status):
        """تحويل حالة المشروع للعربية"""
        status_map = {
            'Planned': 'مخطط',
            'InProgress': 'قيد التنفيذ',
            'Completed': 'مكتمل',
            'OnHold': 'متوقف',
            'Cancelled': 'ملغي'
        }
        return status_map.get(status, status)

class ExcelReportGenerator(BaseReportGenerator):
    """مولد تقارير Excel"""
    
    def generate_report(self, report_type, data, options):
        """إنشاء تقرير Excel"""
        try:
            # إنشاء اسم الملف
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"{report_type}_{timestamp}.xlsx"
            file_path = self.reports_dir / filename
            
            # إنشاء المصنف
            wb = openpyxl.Workbook()
            ws = wb.active
            ws.title = "تقرير المشاريع"
            
            # تنسيق العنوان
            title = self.get_report_title(report_type)
            ws['A1'] = title
            ws['A1'].font = Font(size=16, bold=True, color="1F4E79")
            ws['A1'].alignment = Alignment(horizontal='center')
            ws.merge_cells('A1:F1')
            
            # معلومات التقرير
            row = 3
            ws[f'A{row}'] = "تاريخ الإنشاء:"
            ws[f'B{row}'] = data['generation_date'].strftime("%Y/%m/%d %H:%M")
            row += 1
            ws[f'A{row}'] = "المنشئ:"
            ws[f'B{row}'] = data['generated_by']
            row += 1
            ws[f'A{row}'] = "الفترة:"
            ws[f'B{row}'] = f"من {data['start_date']} إلى {data['end_date']}"
            
            # الإحصائيات
            stats = self.get_statistics(data['start_date'], data['end_date'])
            row += 3
            ws[f'A{row}'] = "الإحصائيات العامة"
            ws[f'A{row}'].font = Font(size=14, bold=True)
            
            row += 1
            stats_data = [
                ['إجمالي المشاريع', stats['total_projects']],
                ['المشاريع المكتملة', stats['completed_projects']],
                ['المشاريع النشطة', stats['active_projects']],
                ['إجمالي الميزانية المخططة', stats['total_planned_budget']],
                ['إجمالي الميزانية الفعلية', stats['total_actual_budget']]
            ]
            
            for stat_name, stat_value in stats_data:
                ws[f'A{row}'] = stat_name
                ws[f'B{row}'] = stat_value
                row += 1
            
            # جدول المشاريع
            projects_data = self.get_projects_data(data['start_date'], data['end_date'], data['projects'])
            
            if projects_data:
                row += 2
                ws[f'A{row}'] = "تفاصيل المشاريع"
                ws[f'A{row}'].font = Font(size=14, bold=True)
                
                row += 1
                headers = ['اسم المشروع', 'النوع', 'الموقع', 'الحالة', 'الميزانية المخططة', 'نسبة الإنجاز']
                for col, header in enumerate(headers, 1):
                    cell = ws.cell(row=row, column=col, value=header)
                    cell.font = Font(bold=True)
                    cell.fill = PatternFill(start_color="D9E1F2", end_color="D9E1F2", fill_type="solid")
                
                # بيانات المشاريع
                for project in projects_data:
                    row += 1
                    ws.cell(row=row, column=1, value=project[1])  # اسم المشروع
                    ws.cell(row=row, column=2, value=project[2])  # النوع
                    ws.cell(row=row, column=3, value=project[3])  # الموقع
                    ws.cell(row=row, column=4, value=self.get_status_arabic(project[9]))  # الحالة
                    ws.cell(row=row, column=5, value=project[6])  # الميزانية
                    ws.cell(row=row, column=6, value=f"{project[10]:.1f}%" if project[10] else "0%")  # نسبة الإنجاز
            
            # تنسيق الأعمدة
            for column in ws.columns:
                max_length = 0
                column_letter = column[0].column_letter
                for cell in column:
                    try:
                        if len(str(cell.value)) > max_length:
                            max_length = len(str(cell.value))
                    except:
                        pass
                adjusted_width = min(max_length + 2, 50)
                ws.column_dimensions[column_letter].width = adjusted_width
            
            # حفظ الملف
            wb.save(file_path)
            
            return str(file_path)
            
        except Exception as e:
            print(f"خطأ في إنشاء تقرير Excel: {e}")
            return None
    
    def get_report_title(self, report_type):
        """الحصول على عنوان التقرير"""
        titles = {
            'project_summary': 'تقرير ملخص المشاريع الهندسية',
            'daily_progress': 'تقرير التقدم اليومي',
            'weekly_progress': 'تقرير التقدم الأسبوعي',
            'monthly_progress': 'تقرير التقدم الشهري',
            'financial_report': 'التقرير المالي'
        }
        return titles.get(report_type, 'تقرير المشاريع الهندسية')
    
    def get_status_arabic(self, status):
        """تحويل حالة المشروع للعربية"""
        status_map = {
            'Planned': 'مخطط',
            'InProgress': 'قيد التنفيذ',
            'Completed': 'مكتمل',
            'OnHold': 'متوقف',
            'Cancelled': 'ملغي'
        }
        return status_map.get(status, status)

class WordReportGenerator(BaseReportGenerator):
    """مولد تقارير Word"""
    
    def generate_report(self, report_type, data, options):
        """إنشاء تقرير Word"""
        if not DOCX_AVAILABLE:
            print("تحذير: python-docx غير متوفر - لا يمكن إنشاء تقارير Word")
            return None

        try:
            # إنشاء اسم الملف
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"{report_type}_{timestamp}.docx"
            file_path = self.reports_dir / filename

            # إنشاء المستند
            doc = Document()
            
            # العنوان
            title = self.get_report_title(report_type)
            title_paragraph = doc.add_heading(title, 0)
            title_paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER
            
            # معلومات التقرير
            doc.add_paragraph()
            info_table = doc.add_table(rows=4, cols=2)
            info_table.style = 'Table Grid'
            
            info_data = [
                ('تاريخ الإنشاء:', data['generation_date'].strftime("%Y/%m/%d %H:%M")),
                ('المنشئ:', data['generated_by']),
                ('الفترة:', f"من {data['start_date']} إلى {data['end_date']}"),
                ('المشاريع:', data['projects'])
            ]
            
            for i, (label, value) in enumerate(info_data):
                info_table.cell(i, 0).text = label
                info_table.cell(i, 1).text = str(value)
            
            # الإحصائيات
            doc.add_heading('الإحصائيات العامة', level=1)
            stats = self.get_statistics(data['start_date'], data['end_date'])
            
            stats_table = doc.add_table(rows=5, cols=2)
            stats_table.style = 'Table Grid'
            
            stats_data = [
                ('إجمالي المشاريع', str(stats['total_projects'])),
                ('المشاريع المكتملة', str(stats['completed_projects'])),
                ('المشاريع النشطة', str(stats['active_projects'])),
                ('إجمالي الميزانية المخططة', f"{stats['total_planned_budget']:,.0f} ريال"),
                ('إجمالي الميزانية الفعلية', f"{stats['total_actual_budget']:,.0f} ريال")
            ]
            
            for i, (label, value) in enumerate(stats_data):
                stats_table.cell(i, 0).text = label
                stats_table.cell(i, 1).text = value
            
            # جدول المشاريع
            projects_data = self.get_projects_data(data['start_date'], data['end_date'], data['projects'])
            
            if projects_data:
                doc.add_heading('تفاصيل المشاريع', level=1)
                
                projects_table = doc.add_table(rows=1, cols=6)
                projects_table.style = 'Table Grid'
                
                # العناوين
                headers = ['اسم المشروع', 'النوع', 'الموقع', 'الحالة', 'الميزانية', 'نسبة الإنجاز']
                for i, header in enumerate(headers):
                    projects_table.cell(0, i).text = header
                
                # البيانات
                for project in projects_data:
                    row_cells = projects_table.add_row().cells
                    row_cells[0].text = project[1] or ""
                    row_cells[1].text = project[2] or ""
                    row_cells[2].text = project[3] or ""
                    row_cells[3].text = self.get_status_arabic(project[9])
                    row_cells[4].text = f"{project[6]:,.0f}" if project[6] else "0"
                    row_cells[5].text = f"{project[10]:.1f}%" if project[10] else "0%"
            
            # حفظ المستند
            doc.save(file_path)
            
            return str(file_path)
            
        except Exception as e:
            print(f"خطأ في إنشاء تقرير Word: {e}")
            return None
    
    def get_report_title(self, report_type):
        """الحصول على عنوان التقرير"""
        titles = {
            'project_summary': 'تقرير ملخص المشاريع الهندسية',
            'daily_progress': 'تقرير التقدم اليومي',
            'weekly_progress': 'تقرير التقدم الأسبوعي',
            'monthly_progress': 'تقرير التقدم الشهري',
            'financial_report': 'التقرير المالي'
        }
        return titles.get(report_type, 'تقرير المشاريع الهندسية')
    
    def get_status_arabic(self, status):
        """تحويل حالة المشروع للعربية"""
        status_map = {
            'Planned': 'مخطط',
            'InProgress': 'قيد التنفيذ',
            'Completed': 'مكتمل',
            'OnHold': 'متوقف',
            'Cancelled': 'ملغي'
        }
        return status_map.get(status, status)
