
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
النافذة الرئيسية لنظام إدارة المشاريع الهندسية
Main Window for Engineering Project Management System
"""

import tkinter as tk
from tkinter import ttk, messagebox
import customtkinter as ctk
from datetime import datetime

# Optional matplotlib import
try:
    import matplotlib.pyplot as plt
    from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
    import matplotlib.dates as mdates
    MATPLOTLIB_AVAILABLE = True
except ImportError:
    MATPLOTLIB_AVAILABLE = False
    print("تحذير: matplotlib غير متوفر - سيتم تعطيل الرسوم البيانية")
    print("Warning: matplotlib not available - charts will be disabled")

class MainWindow:
    """فئة النافذة الرئيسية"""
    
    def __init__(self, user_data, db_manager):
        """تهيئة النافذة الرئيسية"""
        self.user_data = user_data
        self.db_manager = db_manager
        self.window = None
        self.current_frame = None
        
        self.setup_window()
        self.setup_ui()
        self.load_dashboard()
    
    def setup_window(self):
        """إعداد النافذة الرئيسية"""
        self.window = ctk.CTk()
        self.window.title(f"نظام إدارة المشاريع الهندسية - بلدية كفرنجة | {self.user_data['full_name']}")
        self.window.geometry("1400x800")
        self.window.state('zoomed')  # تكبير النافذة
        
        # إعداد الألوان
        self.colors = {
            'primary': '#1f538d',
            'secondary': '#2980b9',
            'success': '#27ae60',
            'warning': '#f39c12',
            'danger': '#e74c3c',
            'light': '#ecf0f1',
            'dark': '#2c3e50'
        }
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # الشريط العلوي
        self.setup_header()
        
        # الشريط الجانبي
        self.setup_sidebar()
        
        # المنطقة الرئيسية
        self.setup_main_area()
    
    def setup_header(self):
        """إعداد الشريط العلوي"""
        header_frame = ctk.CTkFrame(self.window, height=80, fg_color=self.colors['primary'])
        header_frame.pack(fill="x", padx=0, pady=0)
        header_frame.pack_propagate(False)
        
        # عنوان النظام
        title_label = ctk.CTkLabel(
            header_frame,
            text="نظام إدارة المشاريع الهندسية - بلدية كفرنجة",
            font=ctk.CTkFont(size=20, weight="bold"),
            text_color="white"
        )
        title_label.pack(side="right", padx=20, pady=20)
        
        # معلومات المستخدم
        user_frame = ctk.CTkFrame(header_frame, fg_color="transparent")
        user_frame.pack(side="left", padx=20, pady=10)
        
        welcome_label = ctk.CTkLabel(
            user_frame,
            text=f"مرحباً، {self.user_data['full_name']}",
            font=ctk.CTkFont(size=14, weight="bold"),
            text_color="white"
        )
        welcome_label.pack(anchor="w")
        
        role_label = ctk.CTkLabel(
            user_frame,
            text=f"الصلاحية: {self.get_role_name(self.user_data['role'])}",
            font=ctk.CTkFont(size=12),
            text_color="#bdc3c7"
        )
        role_label.pack(anchor="w")
        
        # زر تسجيل الخروج
        logout_button = ctk.CTkButton(
            header_frame,
            text="تسجيل الخروج",
            command=self.logout,
            width=100,
            height=35,
            fg_color="#c0392b",
            hover_color="#a93226"
        )
        logout_button.pack(side="left", padx=(0, 20), pady=22)
    
    def setup_sidebar(self):
        """إعداد الشريط الجانبي"""
        self.sidebar_frame = ctk.CTkFrame(self.window, width=250, fg_color=self.colors['dark'])
        self.sidebar_frame.pack(side="right", fill="y", padx=(0, 0), pady=0)
        self.sidebar_frame.pack_propagate(False)
        
        # قائمة الوظائف
        functions = [
            ("لوحة التحكم", "dashboard", "📊"),
            ("إدارة المشاريع", "projects", "🏗️"),
            ("إدارة المهام", "tasks", "📋"),
            ("إدارة الموظفين", "employees", "👥"),
            ("إدارة المقاولين", "contractors", "🏢"),
            ("إدارة المخزون", "inventory", "📦"),
            ("الإشعارات", "notifications", "🔔"),
            ("التقارير", "reports", "📄"),
            ("النسخ الاحتياطية", "backup", "💾"),
            ("الإعدادات", "settings", "⚙️")
        ]
        
        # عنوان القائمة
        menu_title = ctk.CTkLabel(
            self.sidebar_frame,
            text="القائمة الرئيسية",
            font=ctk.CTkFont(size=16, weight="bold"),
            text_color="white"
        )
        menu_title.pack(pady=(20, 30))
        
        # أزرار القائمة
        self.menu_buttons = {}
        for name, key, icon in functions:
            button = ctk.CTkButton(
                self.sidebar_frame,
                text=f"{icon} {name}",
                command=lambda k=key: self.show_section(k),
                width=220,
                height=45,
                font=ctk.CTkFont(size=14),
                fg_color="transparent",
                hover_color=self.colors['primary'],
                anchor="w"
            )
            button.pack(pady=5, padx=15)
            self.menu_buttons[key] = button
        
        # معلومات النظام
        info_frame = ctk.CTkFrame(self.sidebar_frame, fg_color="transparent")
        info_frame.pack(side="bottom", pady=20)
        
        version_label = ctk.CTkLabel(
            info_frame,
            text="الإصدار 1.0.0",
            font=ctk.CTkFont(size=10),
            text_color="#7f8c8d"
        )
        version_label.pack()
        
        date_label = ctk.CTkLabel(
            info_frame,
            text=datetime.now().strftime("%Y/%m/%d"),
            font=ctk.CTkFont(size=10),
            text_color="#7f8c8d"
        )
        date_label.pack()
    
    def setup_main_area(self):
        """إعداد المنطقة الرئيسية"""
        self.main_frame = ctk.CTkFrame(self.window, fg_color="#f8f9fa")
        self.main_frame.pack(side="left", fill="both", expand=True, padx=0, pady=0)
    
    def get_role_name(self, role):
        """الحصول على اسم الصلاحية بالعربية"""
        roles = {
            'Admin': 'مدير النظام',
            'ProjectManager': 'مدير مشروع',
            'Employee': 'موظف',
            'SystemManager': 'مدير النظام'
        }
        return roles.get(role, role)
    
    def show_section(self, section):
        """عرض قسم معين"""
        # إزالة المحتوى الحالي
        if self.current_frame:
            self.current_frame.destroy()
        
        # تحديث لون الأزرار
        for key, button in self.menu_buttons.items():
            if key == section:
                button.configure(fg_color=self.colors['primary'])
            else:
                button.configure(fg_color="transparent")
        
        # عرض القسم المطلوب
        if section == "dashboard":
            self.load_dashboard()
        elif section == "projects":
            self.load_projects()
        elif section == "tasks":
            self.load_tasks()
        elif section == "employees":
            self.load_employees()
        elif section == "contractors":
            self.load_contractors()
        elif section == "inventory":
            self.load_inventory()
        elif section == "notifications":
            self.load_notifications()
        elif section == "reports":
            self.load_reports()
        elif section == "backup":
            self.load_backup()
        elif section == "settings":
            self.load_settings()
    
    def load_dashboard(self):
        """تحميل لوحة التحكم"""
        self.current_frame = ctk.CTkFrame(self.main_frame)
        self.current_frame.pack(fill="both", expand=True, padx=20, pady=20)
        
        # عنوان لوحة التحكم
        title_label = ctk.CTkLabel(
            self.current_frame,
            text="لوحة التحكم",
            font=ctk.CTkFont(size=24, weight="bold"),
            text_color=self.colors['primary']
        )
        title_label.pack(pady=(20, 30))
        
        # إحصائيات سريعة
        stats_frame = ctk.CTkFrame(self.current_frame)
        stats_frame.pack(fill="x", padx=20, pady=(0, 20))
        
        # الحصول على الإحصائيات
        stats = self.db_manager.get_project_statistics()
        
        # الحصول على إحصائيات المهام
        task_stats = self.get_task_statistics()

        # بطاقات الإحصائيات
        stats_data = [
            ("إجمالي المشاريع", stats.get('total_projects', 0), self.colors['primary']),
            ("المشاريع النشطة", stats.get('active_projects', 0), self.colors['success']),
            ("إجمالي المهام", task_stats.get('total_tasks', 0), self.colors['warning']),
            ("إجمالي الميزانية", f"{stats.get('total_budget', 0):,.0f} ريال", self.colors['secondary'])
        ]
        
        for i, (title, value, color) in enumerate(stats_data):
            stat_card = ctk.CTkFrame(stats_frame, fg_color=color)
            stat_card.grid(row=0, column=i, padx=10, pady=10, sticky="ew")
            stats_frame.grid_columnconfigure(i, weight=1)
            
            value_label = ctk.CTkLabel(
                stat_card,
                text=str(value),
                font=ctk.CTkFont(size=20, weight="bold"),
                text_color="white"
            )
            value_label.pack(pady=(15, 5))
            
            title_label = ctk.CTkLabel(
                stat_card,
                text=title,
                font=ctk.CTkFont(size=12),
                text_color="white"
            )
            title_label.pack(pady=(0, 15))
        
        # المشاريع الحديثة
        recent_frame = ctk.CTkFrame(self.current_frame)
        recent_frame.pack(fill="both", expand=True, padx=20, pady=20)
        
        recent_title = ctk.CTkLabel(
            recent_frame,
            text="المشاريع الحديثة",
            font=ctk.CTkFont(size=18, weight="bold")
        )
        recent_title.pack(pady=(20, 10))
        
        # جدول المشاريع الحديثة
        self.create_projects_table(recent_frame)
    
    def create_projects_table(self, parent):
        """إنشاء جدول المشاريع"""
        # إطار الجدول
        table_frame = ctk.CTkFrame(parent)
        table_frame.pack(fill="both", expand=True, padx=20, pady=10)
        
        # الحصول على بيانات المشاريع
        projects = self.db_manager.get_projects()
        
        if not projects:
            no_data_label = ctk.CTkLabel(
                table_frame,
                text="لا توجد مشاريع حال",
                font=ctk.CTkFont(size=14),
                text_color="#7f8c8d"
            )
            no_data_label.pack(pady=50)
            return
        
        # إنشاء Treeview للجدول
        columns = ("ID", "اسم المشروع", "الحالة", "تاريخ البداية", "تاريخ النهاية", "الميزانية", "نسبة الإنجاز")
        
        tree = ttk.Treeview(table_frame, columns=columns, show="headings", height=10)
        
        # تعريف العناوين
        for col in columns:
            tree.heading(col, text=col)
            tree.column(col, width=150, anchor="center")
        
        # إضافة البيانات
        for project in projects[:10]:  # عرض أول 10 مشاريع
            tree.insert("", "end", values=(
                project[0],  # ID
                project[1],  # اسم المشروع
                project[2],  # الحالة
                project[3].strftime("%Y/%m/%d") if project[3] else "",  # تاريخ البداية
                project[4].strftime("%Y/%m/%d") if project[4] else "",  # تاريخ النهاية
                f"{project[5]:,.0f}" if project[5] else "0",  # الميزانية
                f"{project[6]:.1f}%" if project[6] else "0%"  # نسبة الإنجاز
            ))
        
        # إضافة شريط التمرير
        scrollbar = ttk.Scrollbar(table_frame, orient="vertical", command=tree.yview)
        tree.configure(yscrollcommand=scrollbar.set)
        
        tree.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

    def get_task_statistics(self):
        """الحصول على إحصائيات المهام"""
        try:
            # إحصائيات المهام
            total_tasks = self.db_manager.cursor.execute(
                "SELECT COUNT(*) FROM Tasks"
            ).fetchone()[0]

            active_tasks = self.db_manager.cursor.execute(
                "SELECT COUNT(*) FROM Tasks WHERE Status IN ('NotStarted', 'InProgress')"
            ).fetchone()[0]

            completed_tasks = self.db_manager.cursor.execute(
                "SELECT COUNT(*) FROM Tasks WHERE Status = 'Completed'"
            ).fetchone()[0]

            return {
                'total_tasks': total_tasks,
                'active_tasks': active_tasks,
                'completed_tasks': completed_tasks
            }
        except Exception as e:
            print(f"خطأ في جلب إحصائيات المهام: {e}")
            return {
                'total_tasks': 0,
                'active_tasks': 0,
                'completed_tasks': 0
            }

    def load_projects(self):
        """تحميل قسم إدارة المشاريع"""
        from project_manager import ProjectManager

        project_manager = ProjectManager(self.window, self.db_manager, self.user_data)
        project_manager.show_project_management()
    
    def load_tasks(self):
        """تحميل قسم إدارة المهام"""
        try:
            from advanced_tasks import AdvancedTasksManager

            # إعداد الألوان والخطوط للوحدة المتقدمة
            colors = {
                'primary': '#2c3e50',
                'secondary': '#34495e',
                'success': '#27ae60',
                'warning': '#f39c12',
                'danger': '#e74c3c',
                'info': '#3498db',
                'light': '#ecf0f1',
                'dark': '#2c3e50',
                'white': '#ffffff'
            }

            fonts = {
                'title': ('Arial', 18, 'bold'),
                'heading': ('Arial', 14, 'bold'),
                'subheading': ('Arial', 12, 'bold'),
                'body': ('Arial', 10),
                'small': ('Arial', 8),
                'button': ('Arial', 10, 'bold')
            }

            # إنشاء إطار المحتوى
            self.current_frame = ctk.CTkFrame(self.main_frame)
            self.current_frame.pack(fill="both", expand=True, padx=0, pady=0)

            # تحويل إطار customtkinter إلى tkinter للتوافق مع AdvancedTasksManager
            content_area = tk.Frame(self.current_frame._canvas)
            content_area.pack(fill="both", expand=True)

            # إنشاء مدير المهام المتقدم
            tasks_manager = AdvancedTasksManager(
                parent=self.window,
                db_manager=self.db_manager,
                current_user=self.user_data,
                colors=colors,
                fonts=fonts
            )

            # إنشاء واجهة المهام
            tasks_manager.create_tasks_interface(content_area)

        except ImportError as e:
            # في حالة فشل استيراد الوحدة المتقدمة، عرض واجهة بسيطة
            self.current_frame = ctk.CTkFrame(self.main_frame)
            self.current_frame.pack(fill="both", expand=True, padx=20, pady=20)

            title_label = ctk.CTkLabel(
                self.current_frame,
                text="إدارة المهام",
                font=ctk.CTkFont(size=24, weight="bold")
            )
            title_label.pack(pady=20)

            error_label = ctk.CTkLabel(
                self.current_frame,
                text=f"خطأ في تحميل وحدة المهام المتقدمة: {str(e)}",
                font=ctk.CTkFont(size=14),
                text_color="#e74c3c"
            )
            error_label.pack(pady=20)
        except Exception as e:
            # في حالة أي خطأ آخر
            self.current_frame = ctk.CTkFrame(self.main_frame)
            self.current_frame.pack(fill="both", expand=True, padx=20, pady=20)

            title_label = ctk.CTkLabel(
                self.current_frame,
                text="إدارة المهام",
                font=ctk.CTkFont(size=24, weight="bold")
            )
            title_label.pack(pady=20)

            error_label = ctk.CTkLabel(
                self.current_frame,
                text=f"خطأ: {str(e)}",
                font=ctk.CTkFont(size=14),
                text_color="#e74c3c"
            )
            error_label.pack(pady=20)
    
    def load_employees(self):
        """تحميل قسم إدارة الموظفين"""
        try:
            from advanced_employees import AdvancedEmployeeManager

            employee_manager = AdvancedEmployeeManager(self.window, self.db_manager, self.user_data)
            employee_manager.show_employee_management()

        except ImportError as e:
            # في حالة فشل استيراد الوحدة المتقدمة، عرض واجهة بسيطة
            self.current_frame = ctk.CTkFrame(self.main_frame)
            self.current_frame.pack(fill="both", expand=True, padx=20, pady=20)

            title_label = ctk.CTkLabel(
                self.current_frame,
                text="إدارة الموظفين",
                font=ctk.CTkFont(size=24, weight="bold")
            )
            title_label.pack(pady=20)

            error_label = ctk.CTkLabel(
                self.current_frame,
                text=f"خطأ في تحميل وحدة الموظفين المتقدمة: {str(e)}",
                font=ctk.CTkFont(size=14),
                text_color="#e74c3c"
            )
            error_label.pack(pady=20)
        except Exception as e:
            # في حالة أي خطأ آخر
            self.current_frame = ctk.CTkFrame(self.main_frame)
            self.current_frame.pack(fill="both", expand=True, padx=20, pady=20)

            title_label = ctk.CTkLabel(
                self.current_frame,
                text="إدارة الموظفين",
                font=ctk.CTkFont(size=24, weight="bold")
            )
            title_label.pack(pady=20)

            error_label = ctk.CTkLabel(
                self.current_frame,
                text=f"خطأ: {str(e)}",
                font=ctk.CTkFont(size=14),
                text_color="#e74c3c"
            )
            error_label.pack(pady=20)

    def load_contractors(self):
        """تحميل قسم إدارة المقاولين"""
        try:
            from contractor_manager import ContractorManager

            contractor_manager = ContractorManager(self.window, self.db_manager, self.user_data)
            contractor_manager.show_contractor_management()

        except ImportError as e:
            self.current_frame = ctk.CTkFrame(self.main_frame)
            self.current_frame.pack(fill="both", expand=True, padx=20, pady=20)

            title_label = ctk.CTkLabel(
                self.current_frame,
                text="إدارة المقاولين",
                font=ctk.CTkFont(size=24, weight="bold")
            )
            title_label.pack(pady=20)

            error_label = ctk.CTkLabel(
                self.current_frame,
                text=f"خطأ في تحميل وحدة المقاولين: {str(e)}",
                font=ctk.CTkFont(size=14),
                text_color="#e74c3c"
            )
            error_label.pack(pady=20)
        except Exception as e:
            self.current_frame = ctk.CTkFrame(self.main_frame)
            self.current_frame.pack(fill="both", expand=True, padx=20, pady=20)

            title_label = ctk.CTkLabel(
                self.current_frame,
                text="إدارة المقاولين",
                font=ctk.CTkFont(size=24, weight="bold")
            )
            title_label.pack(pady=20)

            error_label = ctk.CTkLabel(
                self.current_frame,
                text=f"خطأ: {str(e)}",
                font=ctk.CTkFont(size=14),
                text_color="#e74c3c"
            )
            error_label.pack(pady=20)

    def load_inventory(self):
        """تحميل قسم إدارة المخزون"""
        try:
            from inventory_manager import InventoryManager

            inventory_manager = InventoryManager(self.window, self.db_manager, self.user_data)
            inventory_manager.show_inventory_management()

        except ImportError as e:
            self.current_frame = ctk.CTkFrame(self.main_frame)
            self.current_frame.pack(fill="both", expand=True, padx=20, pady=20)

            title_label = ctk.CTkLabel(
                self.current_frame,
                text="إدارة المخزون",
                font=ctk.CTkFont(size=24, weight="bold")
            )
            title_label.pack(pady=20)

            error_label = ctk.CTkLabel(
                self.current_frame,
                text=f"خطأ في تحميل وحدة المخزون: {str(e)}",
                font=ctk.CTkFont(size=14),
                text_color="#e74c3c"
            )
            error_label.pack(pady=20)
        except Exception as e:
            self.current_frame = ctk.CTkFrame(self.main_frame)
            self.current_frame.pack(fill="both", expand=True, padx=20, pady=20)

            title_label = ctk.CTkLabel(
                self.current_frame,
                text="إدارة المخزون",
                font=ctk.CTkFont(size=24, weight="bold")
            )
            title_label.pack(pady=20)

            error_label = ctk.CTkLabel(
                self.current_frame,
                text=f"خطأ: {str(e)}",
                font=ctk.CTkFont(size=14),
                text_color="#e74c3c"
            )
            error_label.pack(pady=20)

    def load_notifications(self):
        """تحميل قسم الإشعارات"""
        try:
            from notifications_system import NotificationSystem

            notification_system = NotificationSystem(self.window, self.db_manager, self.user_data)
            notification_system.show_notifications_window()

        except ImportError as e:
            self.current_frame = ctk.CTkFrame(self.main_frame)
            self.current_frame.pack(fill="both", expand=True, padx=20, pady=20)

            title_label = ctk.CTkLabel(
                self.current_frame,
                text="الإشعارات",
                font=ctk.CTkFont(size=24, weight="bold")
            )
            title_label.pack(pady=20)

            error_label = ctk.CTkLabel(
                self.current_frame,
                text=f"خطأ في تحميل نظام الإشعارات: {str(e)}",
                font=ctk.CTkFont(size=14),
                text_color="#e74c3c"
            )
            error_label.pack(pady=20)
        except Exception as e:
            self.current_frame = ctk.CTkFrame(self.main_frame)
            self.current_frame.pack(fill="both", expand=True, padx=20, pady=20)

            title_label = ctk.CTkLabel(
                self.current_frame,
                text="الإشعارات",
                font=ctk.CTkFont(size=24, weight="bold")
            )
            title_label.pack(pady=20)

            error_label = ctk.CTkLabel(
                self.current_frame,
                text=f"خطأ: {str(e)}",
                font=ctk.CTkFont(size=14),
                text_color="#e74c3c"
            )
            error_label.pack(pady=20)

    def load_reports(self):
        """تحميل قسم التقارير"""
        from report_manager import ReportManager

        report_manager = ReportManager(self.window, self.db_manager, self.user_data)
        report_manager.show_report_management()

    def load_backup(self):
        """تحميل قسم النسخ الاحتياطية"""
        try:
            from backup_system import BackupSystem

            backup_system = BackupSystem(self.window, self.db_manager, self.user_data)
            backup_system.show_backup_management()

        except ImportError as e:
            self.current_frame = ctk.CTkFrame(self.main_frame)
            self.current_frame.pack(fill="both", expand=True, padx=20, pady=20)

            title_label = ctk.CTkLabel(
                self.current_frame,
                text="النسخ الاحتياطية",
                font=ctk.CTkFont(size=24, weight="bold")
            )
            title_label.pack(pady=20)

            error_label = ctk.CTkLabel(
                self.current_frame,
                text=f"خطأ في تحميل نظام النسخ الاحتياطي: {str(e)}",
                font=ctk.CTkFont(size=14),
                text_color="#e74c3c"
            )
            error_label.pack(pady=20)
        except Exception as e:
            self.current_frame = ctk.CTkFrame(self.main_frame)
            self.current_frame.pack(fill="both", expand=True, padx=20, pady=20)

            title_label = ctk.CTkLabel(
                self.current_frame,
                text="النسخ الاحتياطية",
                font=ctk.CTkFont(size=24, weight="bold")
            )
            title_label.pack(pady=20)

            error_label = ctk.CTkLabel(
                self.current_frame,
                text=f"خطأ: {str(e)}",
                font=ctk.CTkFont(size=14),
                text_color="#e74c3c"
            )
            error_label.pack(pady=20)

    def load_settings(self):
        """تحميل قسم الإعدادات"""
        self.current_frame = ctk.CTkFrame(self.main_frame)
        self.current_frame.pack(fill="both", expand=True, padx=20, pady=20)
        
        title_label = ctk.CTkLabel(
            self.current_frame,
            text="الإعدادات",
            font=ctk.CTkFont(size=24, weight="bold")
        )
        title_label.pack(pady=20)
        
        coming_soon_label = ctk.CTkLabel(
            self.current_frame,
            text="قريباً...",
            font=ctk.CTkFont(size=18),
            text_color="#7f8c8d"
        )
        coming_soon_label.pack(pady=50)
    
    def logout(self):
        """تسجيل الخروج"""
        result = messagebox.askyesno(
            "تسجيل الخروج",
            "هل أنت متأكد من تسجيل الخروج؟"
        )
        
        if result:
            self.window.destroy()
            # إعادة تشغيل نافذة تسجيل الدخول
            from login_window import LoginWindow
            login_window = LoginWindow(lambda user_data: None)
            login_window.show()
            login_window.mainloop()
    
    def show(self):
        """عرض النافذة"""
        self.window.deiconify()
        self.window.lift()
        self.window.focus_force()
    
    def mainloop(self):
        """تشغيل حلقة الأحداث"""
        self.window.mainloop()
    
    def destroy(self):
        """إغلاق النافذة"""
        if self.window:
            self.window.destroy()

