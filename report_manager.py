#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام إدارة التقارير لنظام إدارة المشاريع الهندسية
Reports Management System for Engineering Project Management
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import customtkinter as ctk
from datetime import datetime, date, timedelta
from tkcalendar import DateEntry
import os
from pathlib import Path

# استيراد مولدات التقارير
try:
    from report_generators import PDFReportGenerator, ExcelReportGenerator, WordReportGenerator
    REPORT_GENERATORS_AVAILABLE = True
except ImportError as e:
    print(f"تحذير: فشل في استيراد مولدات التقارير: {e}")
    REPORT_GENERATORS_AVAILABLE = False

class ReportManager:
    """فئة إدارة التقارير"""
    
    def __init__(self, parent_window, db_manager, current_user):
        """تهيئة مدير التقارير"""
        self.parent_window = parent_window
        self.db_manager = db_manager
        self.current_user = current_user
        self.window = None
        
        # مولدات التقارير
        if REPORT_GENERATORS_AVAILABLE:
            self.pdf_generator = PDFReportGenerator(db_manager)
            self.excel_generator = ExcelReportGenerator(db_manager)
            self.word_generator = WordReportGenerator(db_manager)
        else:
            self.pdf_generator = None
            self.excel_generator = None
            self.word_generator = None
        
    def show_report_management(self):
        """عرض نافذة إدارة التقارير"""
        self.window = ctk.CTkToplevel(self.parent_window)
        self.window.title("إدارة التقارير")
        self.window.geometry("900x700")
        self.window.transient(self.parent_window)
        self.window.grab_set()
        
        self.setup_ui()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # العنوان الرئيسي
        title_label = ctk.CTkLabel(
            self.window,
            text="إدارة التقارير",
            font=ctk.CTkFont(size=24, weight="bold")
        )
        title_label.pack(pady=20)
        
        # إطار التبويبات
        tabview = ctk.CTkTabview(self.window)
        tabview.pack(fill="both", expand=True, padx=20, pady=20)
        
        # تبويب إنشاء التقارير
        self.create_reports_tab = tabview.add("إنشاء التقارير")
        self.setup_create_reports_tab()
        
        # تبويب التقارير المحفوظة
        self.saved_reports_tab = tabview.add("التقارير المحفوظة")
        self.setup_saved_reports_tab()
        
        # تبويب قوالب التقارير
        self.templates_tab = tabview.add("قوالب التقارير")
        self.setup_templates_tab()
    
    def setup_create_reports_tab(self):
        """إعداد تبويب إنشاء التقارير"""
        # إطار نوع التقرير
        report_type_frame = ctk.CTkFrame(self.create_reports_tab)
        report_type_frame.pack(fill="x", padx=20, pady=20)
        
        type_title = ctk.CTkLabel(
            report_type_frame,
            text="نوع التقرير",
            font=ctk.CTkFont(size=16, weight="bold")
        )
        type_title.pack(pady=(15, 10))
        
        # اختيار نوع التقرير
        self.report_type_var = tk.StringVar(value="project_summary")
        
        report_types = [
            ("تقرير ملخص المشاريع", "project_summary"),
            ("تقرير التقدم اليومي", "daily_progress"),
            ("تقرير التقدم الأسبوعي", "weekly_progress"),
            ("تقرير التقدم الشهري", "monthly_progress"),
            ("التقرير المالي", "financial_report"),
            ("تقرير الموظفين", "employees_report"),
            ("تقرير مخصص", "custom_report")
        ]
        
        for i, (text, value) in enumerate(report_types):
            radio = ctk.CTkRadioButton(
                report_type_frame,
                text=text,
                variable=self.report_type_var,
                value=value,
                command=self.on_report_type_change
            )
            radio.pack(anchor="w", padx=20, pady=2)
        
        # إطار الفترة الزمنية
        period_frame = ctk.CTkFrame(self.create_reports_tab)
        period_frame.pack(fill="x", padx=20, pady=(0, 20))
        
        period_title = ctk.CTkLabel(
            period_frame,
            text="الفترة الزمنية",
            font=ctk.CTkFont(size=16, weight="bold")
        )
        period_title.pack(pady=(15, 10))
        
        # إطار التواريخ
        dates_frame = ctk.CTkFrame(period_frame, fg_color="transparent")
        dates_frame.pack(fill="x", padx=20, pady=(0, 15))
        
        # تاريخ البداية
        start_frame = ctk.CTkFrame(dates_frame, fg_color="transparent")
        start_frame.pack(side="left", fill="x", expand=True, padx=(0, 10))
        
        start_label = ctk.CTkLabel(start_frame, text="من تاريخ:")
        start_label.pack(anchor="w")
        
        self.start_date = DateEntry(
            start_frame,
            width=12,
            background='darkblue',
            foreground='white',
            borderwidth=2,
            date_pattern='yyyy/mm/dd'
        )
        self.start_date.pack(fill="x", pady=(5, 0))
        
        # تاريخ النهاية
        end_frame = ctk.CTkFrame(dates_frame, fg_color="transparent")
        end_frame.pack(side="right", fill="x", expand=True, padx=(10, 0))
        
        end_label = ctk.CTkLabel(end_frame, text="إلى تاريخ:")
        end_label.pack(anchor="w")
        
        self.end_date = DateEntry(
            end_frame,
            width=12,
            background='darkblue',
            foreground='white',
            borderwidth=2,
            date_pattern='yyyy/mm/dd'
        )
        self.end_date.pack(fill="x", pady=(5, 0))
        
        # أزرار الفترات السريعة
        quick_periods_frame = ctk.CTkFrame(period_frame, fg_color="transparent")
        quick_periods_frame.pack(fill="x", padx=20, pady=(0, 20))
        
        quick_label = ctk.CTkLabel(quick_periods_frame, text="فترات سريعة:")
        quick_label.pack(anchor="w", pady=(0, 5))
        
        quick_buttons_frame = ctk.CTkFrame(quick_periods_frame, fg_color="transparent")
        quick_buttons_frame.pack(fill="x")
        
        quick_periods = [
            ("اليوم", 0),
            ("آخر 7 أيام", 7),
            ("آخر 30 يوم", 30),
            ("آخر 3 أشهر", 90),
            ("السنة الحالية", 365)
        ]
        
        for text, days in quick_periods:
            btn = ctk.CTkButton(
                quick_buttons_frame,
                text=text,
                command=lambda d=days: self.set_quick_period(d),
                width=80,
                height=30,
                fg_color="#3498db",
                hover_color="#2980b9"
            )
            btn.pack(side="left", padx=2)
        
        # إطار خيارات التقرير
        options_frame = ctk.CTkFrame(self.create_reports_tab)
        options_frame.pack(fill="x", padx=20, pady=(0, 20))
        
        options_title = ctk.CTkLabel(
            options_frame,
            text="خيارات التقرير",
            font=ctk.CTkFont(size=16, weight="bold")
        )
        options_title.pack(pady=(15, 10))
        
        # اختيار المشاريع
        projects_label = ctk.CTkLabel(options_frame, text="المشاريع:")
        projects_label.pack(anchor="w", padx=20, pady=(0, 5))
        
        self.projects_var = tk.StringVar()
        self.projects_combo = ctk.CTkComboBox(
            options_frame,
            variable=self.projects_var,
            values=["جميع المشاريع"],
            height=35,
            state="readonly"
        )
        self.projects_combo.pack(fill="x", padx=20, pady=(0, 15))
        
        # تنسيق التصدير
        format_label = ctk.CTkLabel(options_frame, text="تنسيق التصدير:")
        format_label.pack(anchor="w", padx=20, pady=(0, 5))
        
        self.export_format_var = tk.StringVar(value="PDF")
        
        formats_frame = ctk.CTkFrame(options_frame, fg_color="transparent")
        formats_frame.pack(fill="x", padx=20, pady=(0, 15))
        
        formats = ["PDF", "Excel", "Word"]
        for fmt in formats:
            radio = ctk.CTkRadioButton(
                formats_frame,
                text=fmt,
                variable=self.export_format_var,
                value=fmt
            )
            radio.pack(side="left", padx=10)
        
        # خيارات إضافية
        self.include_charts_var = tk.BooleanVar(value=True)
        self.include_images_var = tk.BooleanVar(value=False)
        self.include_details_var = tk.BooleanVar(value=True)
        
        charts_check = ctk.CTkCheckBox(
            options_frame,
            text="تضمين الرسوم البيانية",
            variable=self.include_charts_var
        )
        charts_check.pack(anchor="w", padx=20, pady=2)
        
        images_check = ctk.CTkCheckBox(
            options_frame,
            text="تضمين الصور",
            variable=self.include_images_var
        )
        images_check.pack(anchor="w", padx=20, pady=2)
        
        details_check = ctk.CTkCheckBox(
            options_frame,
            text="تضمين التفاصيل الكاملة",
            variable=self.include_details_var
        )
        details_check.pack(anchor="w", padx=20, pady=(2, 20))
        
        # أزرار الإجراءات
        actions_frame = ctk.CTkFrame(self.create_reports_tab)
        actions_frame.pack(fill="x", padx=20, pady=20)
        
        generate_button = ctk.CTkButton(
            actions_frame,
            text="📊 إنشاء التقرير",
            command=self.generate_report,
            width=150,
            height=40,
            fg_color="#27ae60",
            hover_color="#229954",
            font=ctk.CTkFont(size=14, weight="bold")
        )
        generate_button.pack(side="right", padx=10, pady=15)
        
        preview_button = ctk.CTkButton(
            actions_frame,
            text="👁️ معاينة",
            command=self.preview_report,
            width=120,
            height=40,
            fg_color="#3498db",
            hover_color="#2980b9"
        )
        preview_button.pack(side="right", padx=10, pady=15)
        
        # تحميل قائمة المشاريع
        self.load_projects_list()
        
        # تعيين التواريخ الافتراضية
        self.set_quick_period(30)  # آخر 30 يوم
    
    def setup_saved_reports_tab(self):
        """إعداد تبويب التقارير المحفوظة"""
        # جدول التقارير المحفوظة
        table_frame = ctk.CTkFrame(self.saved_reports_tab)
        table_frame.pack(fill="both", expand=True, padx=20, pady=20)
        
        # عنوان
        title_label = ctk.CTkLabel(
            table_frame,
            text="التقارير المحفوظة",
            font=ctk.CTkFont(size=16, weight="bold")
        )
        title_label.pack(pady=(15, 10))
        
        # أعمدة الجدول
        columns = ("ID", "نوع التقرير", "تاريخ الإنشاء", "المنشئ", "الحالة", "مسار الملف")
        
        self.reports_tree = ttk.Treeview(table_frame, columns=columns, show="headings", height=15)
        
        # تعريف العناوين
        for col in columns:
            self.reports_tree.heading(col, text=col)
            self.reports_tree.column(col, width=120, anchor="center")
        
        # شريط التمرير
        scrollbar = ttk.Scrollbar(table_frame, orient="vertical", command=self.reports_tree.yview)
        self.reports_tree.configure(yscrollcommand=scrollbar.set)
        
        self.reports_tree.pack(side="left", fill="both", expand=True, padx=(20, 0), pady=(0, 20))
        scrollbar.pack(side="right", fill="y", pady=(0, 20))
        
        # أزرار الإجراءات
        buttons_frame = ctk.CTkFrame(self.saved_reports_tab)
        buttons_frame.pack(fill="x", padx=20, pady=(0, 20))
        
        open_button = ctk.CTkButton(
            buttons_frame,
            text="📂 فتح التقرير",
            command=self.open_saved_report,
            width=120,
            height=35
        )
        open_button.pack(side="right", padx=5, pady=10)
        
        delete_button = ctk.CTkButton(
            buttons_frame,
            text="🗑️ حذف",
            command=self.delete_saved_report,
            width=100,
            height=35,
            fg_color="#e74c3c",
            hover_color="#c0392b"
        )
        delete_button.pack(side="right", padx=5, pady=10)
        
        # تحميل التقارير المحفوظة
        self.load_saved_reports()
    
    def setup_templates_tab(self):
        """إعداد تبويب قوالب التقارير"""
        # قائمة القوالب
        templates_frame = ctk.CTkFrame(self.templates_tab)
        templates_frame.pack(fill="both", expand=True, padx=20, pady=20)
        
        title_label = ctk.CTkLabel(
            templates_frame,
            text="قوالب التقارير",
            font=ctk.CTkFont(size=16, weight="bold")
        )
        title_label.pack(pady=(15, 10))
        
        # قائمة القوالب المتاحة
        templates_list = [
            "قالب التقرير الأساسي",
            "قالب التقرير المالي",
            "قالب تقرير التقدم",
            "قالب التقرير الشهري",
            "قالب التقرير السنوي"
        ]
        
        for template in templates_list:
            template_frame = ctk.CTkFrame(templates_frame)
            template_frame.pack(fill="x", padx=20, pady=5)
            
            template_label = ctk.CTkLabel(
                template_frame,
                text=template,
                font=ctk.CTkFont(size=12)
            )
            template_label.pack(side="left", padx=15, pady=10)
            
            edit_button = ctk.CTkButton(
                template_frame,
                text="تعديل",
                width=80,
                height=30,
                fg_color="#f39c12",
                hover_color="#e67e22"
            )
            edit_button.pack(side="right", padx=5, pady=5)
            
            preview_button = ctk.CTkButton(
                template_frame,
                text="معاينة",
                width=80,
                height=30,
                fg_color="#3498db",
                hover_color="#2980b9"
            )
            preview_button.pack(side="right", padx=5, pady=5)
    
    def load_projects_list(self):
        """تحميل قائمة المشاريع"""
        projects = self.db_manager.get_projects()
        if projects:
            project_names = ["جميع المشاريع"] + [f"{proj[1]} (ID: {proj[0]})" for proj in projects]
            self.projects_combo.configure(values=project_names)
    
    def set_quick_period(self, days):
        """تعيين فترة زمنية سريعة"""
        end_date = date.today()
        start_date = end_date - timedelta(days=days)
        
        self.start_date.set_date(start_date)
        self.end_date.set_date(end_date)
    
    def on_report_type_change(self):
        """معالج تغيير نوع التقرير"""
        report_type = self.report_type_var.get()
        
        # تعديل الخيارات حسب نوع التقرير
        if report_type == "daily_progress":
            self.set_quick_period(0)  # اليوم فقط
        elif report_type == "weekly_progress":
            self.set_quick_period(7)  # آخر 7 أيام
        elif report_type == "monthly_progress":
            self.set_quick_period(30)  # آخر 30 يوم
    
    def generate_report(self):
        """إنشاء التقرير"""
        if not REPORT_GENERATORS_AVAILABLE:
            messagebox.showerror("خطأ", "مولدات التقارير غير متوفرة. يرجى تثبيت المكتبات المطلوبة:\npip install reportlab python-docx")
            return

        try:
            # جمع البيانات
            report_data = self.collect_report_data()

            # اختيار مولد التقرير حسب التنسيق
            export_format = self.export_format_var.get()

            if export_format == "PDF":
                generator = self.pdf_generator
            elif export_format == "Excel":
                generator = self.excel_generator
            elif export_format == "Word":
                generator = self.word_generator
            else:
                messagebox.showerror("خطأ", "تنسيق غير مدعوم")
                return

            if generator is None:
                messagebox.showerror("خطأ", f"مولد تقارير {export_format} غير متوفر")
                return
            
            # إنشاء التقرير
            file_path = generator.generate_report(
                report_type=self.report_type_var.get(),
                data=report_data,
                options={
                    'include_charts': self.include_charts_var.get(),
                    'include_images': self.include_images_var.get(),
                    'include_details': self.include_details_var.get()
                }
            )
            
            if file_path:
                # حفظ معلومات التقرير في قاعدة البيانات
                self.save_report_info(file_path)
                
                # عرض رسالة نجاح
                result = messagebox.askyesno(
                    "تم إنشاء التقرير",
                    f"تم إنشاء التقرير بنجاح!\nمسار الملف: {file_path}\n\nهل تريد فتح التقرير الآن؟"
                )
                
                if result:
                    os.startfile(file_path)  # فتح الملف بالبرنامج الافتراضي
                
                # تحديث قائمة التقارير المحفوظة
                self.load_saved_reports()
            else:
                messagebox.showerror("خطأ", "فشل في إنشاء التقرير")
        
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء إنشاء التقرير:\n{str(e)}")
    
    def collect_report_data(self):
        """جمع بيانات التقرير"""
        return {
            'report_type': self.report_type_var.get(),
            'start_date': self.start_date.get_date(),
            'end_date': self.end_date.get_date(),
            'projects': self.projects_var.get(),
            'generated_by': self.current_user['full_name'],
            'generation_date': datetime.now()
        }
    
    def save_report_info(self, file_path):
        """حفظ معلومات التقرير في قاعدة البيانات"""
        query = """
            INSERT INTO Reports (ReportType, ReportTitle, GeneratedBy, GenerationDate,
                               ReportPeriodStart, ReportPeriodEnd, FilePath, Status)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        """
        
        params = (
            self.report_type_var.get(),
            f"تقرير {self.get_report_type_name()} - {datetime.now().strftime('%Y/%m/%d')}",
            self.current_user['user_id'],
            datetime.now(),
            self.start_date.get_date(),
            self.end_date.get_date(),
            file_path,
            'Generated'
        )
        
        self.db_manager.execute_non_query(query, params)
    
    def get_report_type_name(self):
        """الحصول على اسم نوع التقرير"""
        types = {
            'project_summary': 'ملخص المشاريع',
            'daily_progress': 'التقدم اليومي',
            'weekly_progress': 'التقدم الأسبوعي',
            'monthly_progress': 'التقدم الشهري',
            'financial_report': 'التقرير المالي',
            'employees_report': 'تقرير الموظفين',
            'custom_report': 'تقرير مخصص'
        }
        return types.get(self.report_type_var.get(), 'تقرير')
    
    def preview_report(self):
        """معاينة التقرير"""
        messagebox.showinfo("معاينة", "ميزة المعاينة قيد التطوير")
    
    def load_saved_reports(self):
        """تحميل التقارير المحفوظة"""
        # مسح البيانات الحالية
        for item in self.reports_tree.get_children():
            self.reports_tree.delete(item)
        
        # استعلام التقارير
        query = """
            SELECT r.ReportID, r.ReportType, r.GenerationDate, u.Username, r.Status, r.FilePath
            FROM Reports r
            LEFT JOIN Users u ON r.GeneratedBy = u.UserID
            ORDER BY r.GenerationDate DESC
        """
        
        reports = self.db_manager.execute_query(query)
        
        if reports:
            for report in reports:
                report_id = report[0]
                report_type = self.get_report_type_name_by_value(report[1])
                generation_date = report[2].strftime("%Y/%m/%d %H:%M") if report[2] else ""
                generated_by = report[3] if report[3] else "غير معروف"
                status = "مُنشأ" if report[4] == "Generated" else report[4]
                file_path = report[5] if report[5] else ""
                
                self.reports_tree.insert("", "end", values=(
                    report_id, report_type, generation_date, generated_by, status, file_path
                ))
    
    def get_report_type_name_by_value(self, value):
        """الحصول على اسم نوع التقرير من القيمة"""
        types = {
            'project_summary': 'ملخص المشاريع',
            'daily_progress': 'التقدم اليومي',
            'weekly_progress': 'التقدم الأسبوعي',
            'monthly_progress': 'التقدم الشهري',
            'financial_report': 'التقرير المالي',
            'employees_report': 'تقرير الموظفين',
            'custom_report': 'تقرير مخصص'
        }
        return types.get(value, value)
    
    def open_saved_report(self):
        """فتح تقرير محفوظ"""
        selected = self.reports_tree.selection()
        if not selected:
            messagebox.showwarning("تحذير", "يرجى اختيار تقرير لفتحه")
            return
        
        file_path = self.reports_tree.item(selected[0])['values'][5]
        
        if file_path and os.path.exists(file_path):
            os.startfile(file_path)
        else:
            messagebox.showerror("خطأ", "الملف غير موجود أو تم حذفه")
    
    def delete_saved_report(self):
        """حذف تقرير محفوظ"""
        selected = self.reports_tree.selection()
        if not selected:
            messagebox.showwarning("تحذير", "يرجى اختيار تقرير للحذف")
            return
        
        report_id = self.reports_tree.item(selected[0])['values'][0]
        file_path = self.reports_tree.item(selected[0])['values'][5]
        
        result = messagebox.askyesno(
            "تأكيد الحذف",
            "هل أنت متأكد من حذف هذا التقرير؟\nسيتم حذف الملف من النظام أيضاً."
        )
        
        if result:
            # حذف من قاعدة البيانات
            query = "DELETE FROM Reports WHERE ReportID = ?"
            if self.db_manager.execute_non_query(query, (report_id,)):
                # حذف الملف
                if file_path and os.path.exists(file_path):
                    try:
                        os.remove(file_path)
                    except:
                        pass
                
                messagebox.showinfo("نجح", "تم حذف التقرير بنجاح")
                self.load_saved_reports()
            else:
                messagebox.showerror("خطأ", "فشل في حذف التقرير")
