#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام إدارة المخزون والمواد
Inventory and Materials Management System
"""

import tkinter as tk
from tkinter import ttk, messagebox
import customtkinter as ctk
from datetime import datetime, date
from tkcalendar import DateEntry

class InventoryManager:
    """مدير المخزون والمواد"""
    
    def __init__(self, parent_window, db_manager, current_user):
        self.parent_window = parent_window
        self.db_manager = db_manager
        self.current_user = current_user
        self.window = None
        
        # إعداد الألوان
        self.colors = {
            'primary': '#1f538d',
            'secondary': '#2980b9',
            'success': '#27ae60',
            'warning': '#f39c12',
            'danger': '#e74c3c',
            'light': '#ecf0f1',
            'dark': '#2c3e50'
        }
        
    def show_inventory_management(self):
        """عرض نافذة إدارة المخزون"""
        self.window = ctk.CTkToplevel(self.parent_window)
        self.window.title("إدارة المخزون والمواد")
        self.window.geometry("1200x800")
        self.window.transient(self.parent_window)
        self.window.grab_set()
        
        self.setup_ui()
        self.load_inventory()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # العنوان الرئيسي
        title_label = ctk.CTkLabel(
            self.window,
            text="إدارة المخزون والمواد",
            font=ctk.CTkFont(size=24, weight="bold")
        )
        title_label.pack(pady=20)
        
        # إطار التبويبات
        self.tabview = ctk.CTkTabview(self.window)
        self.tabview.pack(fill="both", expand=True, padx=20, pady=20)
        
        # تبويب المواد
        self.materials_tab = self.tabview.add("المواد")
        self.setup_materials_tab()
        
        # تبويب المعدات
        self.equipment_tab = self.tabview.add("المعدات")
        self.setup_equipment_tab()
        
        # تبويب المخازن
        self.warehouses_tab = self.tabview.add("المخازن")
        self.setup_warehouses_tab()
        
        # تبويب الحركات
        self.transactions_tab = self.tabview.add("حركات المخزون")
        self.setup_transactions_tab()
        
        # تبويب التقارير
        self.reports_tab = self.tabview.add("تقارير المخزون")
        self.setup_reports_tab()
        
    def setup_materials_tab(self):
        """إعداد تبويب المواد"""
        # شريط الأدوات
        toolbar_frame = ctk.CTkFrame(self.materials_tab)
        toolbar_frame.pack(fill="x", padx=10, pady=10)
        
        # أزرار الأدوات
        add_btn = ctk.CTkButton(
            toolbar_frame,
            text="➕ مادة جديدة",
            command=self.add_material,
            width=120,
            height=35,
            fg_color=self.colors['success']
        )
        add_btn.pack(side="right", padx=5, pady=5)
        
        edit_btn = ctk.CTkButton(
            toolbar_frame,
            text="✏️ تعديل",
            command=self.edit_material,
            width=100,
            height=35,
            fg_color=self.colors['warning']
        )
        edit_btn.pack(side="right", padx=5, pady=5)
        
        # شريط البحث
        search_frame = ctk.CTkFrame(toolbar_frame, fg_color="transparent")
        search_frame.pack(side="left", padx=5, pady=5)
        
        search_label = ctk.CTkLabel(search_frame, text="البحث:")
        search_label.pack(side="left", padx=5)
        
        self.search_var = tk.StringVar()
        search_entry = ctk.CTkEntry(
            search_frame,
            textvariable=self.search_var,
            placeholder_text="اسم المادة أو الرمز...",
            width=200
        )
        search_entry.pack(side="left", padx=5)
        search_entry.bind('<KeyRelease>', self.search_materials)
        
        # جدول المواد
        table_frame = ctk.CTkFrame(self.materials_tab)
        table_frame.pack(fill="both", expand=True, padx=10, pady=10)
        
        # أعمدة الجدول
        columns = ("الرمز", "اسم المادة", "الفئة", "الوحدة", "الكمية المتاحة", "الحد الأدنى", "السعر", "المورد", "الحالة")
        
        self.materials_tree = ttk.Treeview(table_frame, columns=columns, show="headings", height=20)
        
        # تعريف العناوين
        for col in columns:
            self.materials_tree.heading(col, text=col)
            self.materials_tree.column(col, width=120, anchor="center")
        
        # شريط التمرير
        scrollbar_y = ttk.Scrollbar(table_frame, orient="vertical", command=self.materials_tree.yview)
        scrollbar_x = ttk.Scrollbar(table_frame, orient="horizontal", command=self.materials_tree.xview)
        
        self.materials_tree.configure(yscrollcommand=scrollbar_y.set, xscrollcommand=scrollbar_x.set)
        
        # تخطيط الجدول
        self.materials_tree.pack(side="left", fill="both", expand=True)
        scrollbar_y.pack(side="right", fill="y")
        scrollbar_x.pack(side="bottom", fill="x")
        
        # ربط الأحداث
        self.materials_tree.bind('<Double-1>', self.edit_material)
        
    def setup_equipment_tab(self):
        """إعداد تبويب المعدات"""
        # شريط الأدوات
        toolbar_frame = ctk.CTkFrame(self.equipment_tab)
        toolbar_frame.pack(fill="x", padx=10, pady=10)
        
        add_equipment_btn = ctk.CTkButton(
            toolbar_frame,
            text="➕ معدة جديدة",
            command=self.add_equipment,
            width=120,
            height=35,
            fg_color=self.colors['success']
        )
        add_equipment_btn.pack(side="right", padx=5, pady=5)
        
        # جدول المعدات
        equipment_frame = ctk.CTkFrame(self.equipment_tab)
        equipment_frame.pack(fill="both", expand=True, padx=10, pady=10)
        
        equipment_columns = ("الرمز", "اسم المعدة", "النوع", "الموديل", "تاريخ الشراء", "الحالة", "الموقع", "المسؤول")
        
        self.equipment_tree = ttk.Treeview(equipment_frame, columns=equipment_columns, show="headings", height=15)
        
        for col in equipment_columns:
            self.equipment_tree.heading(col, text=col)
            self.equipment_tree.column(col, width=120, anchor="center")
        
        self.equipment_tree.pack(fill="both", expand=True)
        
    def setup_warehouses_tab(self):
        """إعداد تبويب المخازن"""
        # شريط الأدوات
        toolbar_frame = ctk.CTkFrame(self.warehouses_tab)
        toolbar_frame.pack(fill="x", padx=10, pady=10)
        
        add_warehouse_btn = ctk.CTkButton(
            toolbar_frame,
            text="➕ مخزن جديد",
            command=self.add_warehouse,
            width=120,
            height=35,
            fg_color=self.colors['success']
        )
        add_warehouse_btn.pack(side="right", padx=5, pady=5)
        
        # جدول المخازن
        warehouses_frame = ctk.CTkFrame(self.warehouses_tab)
        warehouses_frame.pack(fill="both", expand=True, padx=10, pady=10)
        
        warehouse_columns = ("رمز المخزن", "اسم المخزن", "الموقع", "السعة", "المسؤول", "عدد المواد", "الحالة")
        
        self.warehouses_tree = ttk.Treeview(warehouses_frame, columns=warehouse_columns, show="headings", height=15)
        
        for col in warehouse_columns:
            self.warehouses_tree.heading(col, text=col)
            self.warehouses_tree.column(col, width=120, anchor="center")
        
        self.warehouses_tree.pack(fill="both", expand=True)
        
    def setup_transactions_tab(self):
        """إعداد تبويب حركات المخزون"""
        # شريط الأدوات
        toolbar_frame = ctk.CTkFrame(self.transactions_tab)
        toolbar_frame.pack(fill="x", padx=10, pady=10)
        
        # أزرار العمليات
        receive_btn = ctk.CTkButton(
            toolbar_frame,
            text="📥 استلام",
            command=self.receive_materials,
            width=100,
            height=35,
            fg_color=self.colors['success']
        )
        receive_btn.pack(side="right", padx=5, pady=5)
        
        issue_btn = ctk.CTkButton(
            toolbar_frame,
            text="📤 صرف",
            command=self.issue_materials,
            width=100,
            height=35,
            fg_color=self.colors['warning']
        )
        issue_btn.pack(side="right", padx=5, pady=5)
        
        transfer_btn = ctk.CTkButton(
            toolbar_frame,
            text="🔄 نقل",
            command=self.transfer_materials,
            width=100,
            height=35,
            fg_color=self.colors['secondary']
        )
        transfer_btn.pack(side="right", padx=5, pady=5)
        
        # فلتر التاريخ
        date_frame = ctk.CTkFrame(toolbar_frame, fg_color="transparent")
        date_frame.pack(side="left", padx=5, pady=5)
        
        date_label = ctk.CTkLabel(date_frame, text="من تاريخ:")
        date_label.pack(side="left", padx=5)
        
        self.from_date = DateEntry(
            date_frame,
            width=12,
            background='darkblue',
            foreground='white',
            borderwidth=2,
            date_pattern='yyyy/mm/dd'
        )
        self.from_date.pack(side="left", padx=5)
        
        # جدول الحركات
        transactions_frame = ctk.CTkFrame(self.transactions_tab)
        transactions_frame.pack(fill="both", expand=True, padx=10, pady=10)
        
        transaction_columns = ("التاريخ", "نوع العملية", "المادة", "الكمية", "المخزن", "المشروع", "المسؤول", "ملاحظات")
        
        self.transactions_tree = ttk.Treeview(transactions_frame, columns=transaction_columns, show="headings", height=15)
        
        for col in transaction_columns:
            self.transactions_tree.heading(col, text=col)
            self.transactions_tree.column(col, width=120, anchor="center")
        
        self.transactions_tree.pack(fill="both", expand=True)
        
    def setup_reports_tab(self):
        """إعداد تبويب تقارير المخزون"""
        # إطار التقارير
        reports_frame = ctk.CTkFrame(self.reports_tab)
        reports_frame.pack(fill="both", expand=True, padx=10, pady=10)
        
        # عنوان
        reports_title = ctk.CTkLabel(
            reports_frame,
            text="تقارير المخزون",
            font=ctk.CTkFont(size=18, weight="bold")
        )
        reports_title.pack(pady=20)
        
        # أزرار التقارير
        reports_buttons_frame = ctk.CTkFrame(reports_frame)
        reports_buttons_frame.pack(pady=20)
        
        # تقرير المواد الناقصة
        low_stock_btn = ctk.CTkButton(
            reports_buttons_frame,
            text="📊 تقرير المواد الناقصة",
            command=self.generate_low_stock_report,
            width=200,
            height=40,
            fg_color=self.colors['danger']
        )
        low_stock_btn.pack(pady=10)
        
        # تقرير حركات المخزون
        movements_btn = ctk.CTkButton(
            reports_buttons_frame,
            text="📈 تقرير حركات المخزون",
            command=self.generate_movements_report,
            width=200,
            height=40,
            fg_color=self.colors['primary']
        )
        movements_btn.pack(pady=10)
        
        # تقرير قيمة المخزون
        value_btn = ctk.CTkButton(
            reports_buttons_frame,
            text="💰 تقرير قيمة المخزون",
            command=self.generate_value_report,
            width=200,
            height=40,
            fg_color=self.colors['success']
        )
        value_btn.pack(pady=10)
        
    def load_inventory(self):
        """تحميل بيانات المخزون"""
        try:
            # مسح البيانات الحالية
            for item in self.materials_tree.get_children():
                self.materials_tree.delete(item)
            
            # بيانات وهمية للعرض
            sample_materials = [
                ("MAT001", "أسمنت", "مواد بناء", "كيس", "150", "50", "25.00", "شركة الأسمنت", "متوفر"),
                ("MAT002", "حديد تسليح", "مواد بناء", "طن", "5", "2", "2500.00", "مصنع الحديد", "ناقص"),
                ("MAT003", "رمل", "مواد بناء", "متر مكعب", "100", "20", "15.00", "محجر الرمل", "متوفر"),
                ("MAT004", "حصى", "مواد بناء", "متر مكعب", "80", "30", "20.00", "محجر الحصى", "متوفر"),
                ("MAT005", "بلاط", "تشطيبات", "متر مربع", "200", "50", "45.00", "مصنع البلاط", "متوفر")
            ]
            
            # إضافة البيانات للجدول
            for material in sample_materials:
                # تلوين الصفوف حسب الحالة
                if material[8] == "ناقص":
                    self.materials_tree.insert("", "end", values=material, tags=("low_stock",))
                else:
                    self.materials_tree.insert("", "end", values=material)
            
            # تنسيق الألوان
            self.materials_tree.tag_configure("low_stock", background="#ffebee")
                
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تحميل بيانات المخزون:\n{str(e)}")
    
    def search_materials(self, event=None):
        """البحث في المواد"""
        messagebox.showinfo("قيد التطوير", "ميزة البحث قيد التطوير")
    
    def add_material(self):
        """إضافة مادة جديدة"""
        messagebox.showinfo("قيد التطوير", "ميزة إضافة مادة جديدة قيد التطوير")
    
    def edit_material(self, event=None):
        """تعديل مادة"""
        messagebox.showinfo("قيد التطوير", "ميزة تعديل المادة قيد التطوير")
    
    def add_equipment(self):
        """إضافة معدة جديدة"""
        messagebox.showinfo("قيد التطوير", "ميزة إضافة معدة جديدة قيد التطوير")
    
    def add_warehouse(self):
        """إضافة مخزن جديد"""
        messagebox.showinfo("قيد التطوير", "ميزة إضافة مخزن جديد قيد التطوير")
    
    def receive_materials(self):
        """استلام مواد"""
        messagebox.showinfo("قيد التطوير", "ميزة استلام المواد قيد التطوير")
    
    def issue_materials(self):
        """صرف مواد"""
        messagebox.showinfo("قيد التطوير", "ميزة صرف المواد قيد التطوير")
    
    def transfer_materials(self):
        """نقل مواد"""
        messagebox.showinfo("قيد التطوير", "ميزة نقل المواد قيد التطوير")
    
    def generate_low_stock_report(self):
        """إنشاء تقرير المواد الناقصة"""
        messagebox.showinfo("قيد التطوير", "تقرير المواد الناقصة قيد التطوير")
    
    def generate_movements_report(self):
        """إنشاء تقرير حركات المخزون"""
        messagebox.showinfo("قيد التطوير", "تقرير حركات المخزون قيد التطوير")
    
    def generate_value_report(self):
        """إنشاء تقرير قيمة المخزون"""
        messagebox.showinfo("قيد التطوير", "تقرير قيمة المخزون قيد التطوير")
