#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام إدارة الموظفين المتقدم
Advanced Employee Management System
"""

import tkinter as tk
from tkinter import ttk, messagebox
import customtkinter as ctk
from datetime import datetime, date
from tkcalendar import DateEntry

class AdvancedEmployeeManager:
    """مدير الموظفين المتقدم"""
    
    def __init__(self, parent_window, db_manager, current_user):
        self.parent_window = parent_window
        self.db_manager = db_manager
        self.current_user = current_user
        self.window = None
        
        # إعداد الألوان
        self.colors = {
            'primary': '#1f538d',
            'secondary': '#2980b9',
            'success': '#27ae60',
            'warning': '#f39c12',
            'danger': '#e74c3c',
            'light': '#ecf0f1',
            'dark': '#2c3e50'
        }
        
    def show_employee_management(self):
        """عرض نافذة إدارة الموظفين"""
        self.window = ctk.CTkToplevel(self.parent_window)
        self.window.title("إدارة الموظفين المتقدمة")
        self.window.geometry("1200x800")
        self.window.transient(self.parent_window)
        self.window.grab_set()
        
        self.setup_ui()
        self.load_employees()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # العنوان الرئيسي
        title_label = ctk.CTkLabel(
            self.window,
            text="إدارة الموظفين المتقدمة",
            font=ctk.CTkFont(size=24, weight="bold")
        )
        title_label.pack(pady=20)
        
        # إطار التبويبات
        self.tabview = ctk.CTkTabview(self.window)
        self.tabview.pack(fill="both", expand=True, padx=20, pady=20)
        
        # تبويب الموظفين
        self.employees_tab = self.tabview.add("الموظفين")
        self.setup_employees_tab()
        
        # تبويب الأقسام
        self.departments_tab = self.tabview.add("الأقسام")
        self.setup_departments_tab()
        
        # تبويب الحضور
        self.attendance_tab = self.tabview.add("الحضور والانصراف")
        self.setup_attendance_tab()
        
        # تبويب الإجازات
        self.leaves_tab = self.tabview.add("الإجازات")
        self.setup_leaves_tab()
        
        # تبويب التقييم
        self.performance_tab = self.tabview.add("تقييم الأداء")
        self.setup_performance_tab()
        
    def setup_employees_tab(self):
        """إعداد تبويب الموظفين"""
        # شريط الأدوات
        toolbar_frame = ctk.CTkFrame(self.employees_tab)
        toolbar_frame.pack(fill="x", padx=10, pady=10)
        
        # أزرار الأدوات
        add_btn = ctk.CTkButton(
            toolbar_frame,
            text="➕ موظف جديد",
            command=self.add_employee,
            width=120,
            height=35,
            fg_color=self.colors['success']
        )
        add_btn.pack(side="right", padx=5, pady=5)
        
        edit_btn = ctk.CTkButton(
            toolbar_frame,
            text="✏️ تعديل",
            command=self.edit_employee,
            width=100,
            height=35,
            fg_color=self.colors['warning']
        )
        edit_btn.pack(side="right", padx=5, pady=5)
        
        delete_btn = ctk.CTkButton(
            toolbar_frame,
            text="🗑️ حذف",
            command=self.delete_employee,
            width=100,
            height=35,
            fg_color=self.colors['danger']
        )
        delete_btn.pack(side="right", padx=5, pady=5)
        
        # شريط البحث
        search_frame = ctk.CTkFrame(toolbar_frame, fg_color="transparent")
        search_frame.pack(side="left", padx=5, pady=5)
        
        search_label = ctk.CTkLabel(search_frame, text="البحث:")
        search_label.pack(side="left", padx=5)
        
        self.search_var = tk.StringVar()
        search_entry = ctk.CTkEntry(
            search_frame,
            textvariable=self.search_var,
            placeholder_text="اسم الموظف أو القسم...",
            width=200
        )
        search_entry.pack(side="left", padx=5)
        search_entry.bind('<KeyRelease>', self.search_employees)
        
        # جدول الموظفين
        table_frame = ctk.CTkFrame(self.employees_tab)
        table_frame.pack(fill="both", expand=True, padx=10, pady=10)
        
        # أعمدة الجدول
        columns = ("ID", "الاسم الكامل", "القسم", "المنصب", "البريد الإلكتروني", "الهاتف", "تاريخ التوظيف", "الحالة")
        
        self.employees_tree = ttk.Treeview(table_frame, columns=columns, show="headings", height=20)
        
        # تعريف العناوين
        for col in columns:
            self.employees_tree.heading(col, text=col)
            self.employees_tree.column(col, width=120, anchor="center")
        
        # شريط التمرير
        scrollbar_y = ttk.Scrollbar(table_frame, orient="vertical", command=self.employees_tree.yview)
        scrollbar_x = ttk.Scrollbar(table_frame, orient="horizontal", command=self.employees_tree.xview)
        
        self.employees_tree.configure(yscrollcommand=scrollbar_y.set, xscrollcommand=scrollbar_x.set)
        
        # تخطيط الجدول
        self.employees_tree.pack(side="left", fill="both", expand=True)
        scrollbar_y.pack(side="right", fill="y")
        scrollbar_x.pack(side="bottom", fill="x")
        
        # ربط الأحداث
        self.employees_tree.bind('<Double-1>', self.edit_employee)
        
    def setup_departments_tab(self):
        """إعداد تبويب الأقسام"""
        # شريط الأدوات
        toolbar_frame = ctk.CTkFrame(self.departments_tab)
        toolbar_frame.pack(fill="x", padx=10, pady=10)
        
        add_dept_btn = ctk.CTkButton(
            toolbar_frame,
            text="➕ قسم جديد",
            command=self.add_department,
            width=120,
            height=35,
            fg_color=self.colors['success']
        )
        add_dept_btn.pack(side="right", padx=5, pady=5)
        
        # جدول الأقسام
        dept_frame = ctk.CTkFrame(self.departments_tab)
        dept_frame.pack(fill="both", expand=True, padx=10, pady=10)
        
        dept_columns = ("ID", "اسم القسم", "رمز القسم", "المدير", "عدد الموظفين", "الميزانية", "الحالة")
        
        self.departments_tree = ttk.Treeview(dept_frame, columns=dept_columns, show="headings", height=15)
        
        for col in dept_columns:
            self.departments_tree.heading(col, text=col)
            self.departments_tree.column(col, width=120, anchor="center")
        
        self.departments_tree.pack(fill="both", expand=True)
        
    def setup_attendance_tab(self):
        """إعداد تبويب الحضور والانصراف"""
        # إطار التحكم
        control_frame = ctk.CTkFrame(self.attendance_tab)
        control_frame.pack(fill="x", padx=10, pady=10)
        
        # اختيار التاريخ
        date_label = ctk.CTkLabel(control_frame, text="التاريخ:")
        date_label.pack(side="left", padx=5)
        
        self.attendance_date = DateEntry(
            control_frame,
            width=12,
            background='darkblue',
            foreground='white',
            borderwidth=2,
            date_pattern='yyyy/mm/dd'
        )
        self.attendance_date.pack(side="left", padx=5)
        
        # زر تحديث
        refresh_btn = ctk.CTkButton(
            control_frame,
            text="🔄 تحديث",
            command=self.load_attendance,
            width=100,
            height=35
        )
        refresh_btn.pack(side="left", padx=10)
        
        # زر تسجيل حضور
        checkin_btn = ctk.CTkButton(
            control_frame,
            text="✅ تسجيل حضور",
            command=self.record_checkin,
            width=120,
            height=35,
            fg_color=self.colors['success']
        )
        checkin_btn.pack(side="right", padx=5)
        
        # جدول الحضور
        attendance_frame = ctk.CTkFrame(self.attendance_tab)
        attendance_frame.pack(fill="both", expand=True, padx=10, pady=10)
        
        attendance_columns = ("الموظف", "تاريخ الحضور", "وقت الدخول", "وقت الخروج", "ساعات العمل", "الحالة")
        
        self.attendance_tree = ttk.Treeview(attendance_frame, columns=attendance_columns, show="headings", height=15)
        
        for col in attendance_columns:
            self.attendance_tree.heading(col, text=col)
            self.attendance_tree.column(col, width=120, anchor="center")
        
        self.attendance_tree.pack(fill="both", expand=True)
        
    def setup_leaves_tab(self):
        """إعداد تبويب الإجازات"""
        # شريط الأدوات
        toolbar_frame = ctk.CTkFrame(self.leaves_tab)
        toolbar_frame.pack(fill="x", padx=10, pady=10)
        
        add_leave_btn = ctk.CTkButton(
            toolbar_frame,
            text="➕ طلب إجازة",
            command=self.add_leave_request,
            width=120,
            height=35,
            fg_color=self.colors['success']
        )
        add_leave_btn.pack(side="right", padx=5, pady=5)
        
        # جدول الإجازات
        leaves_frame = ctk.CTkFrame(self.leaves_tab)
        leaves_frame.pack(fill="both", expand=True, padx=10, pady=10)
        
        leaves_columns = ("الموظف", "نوع الإجازة", "تاريخ البداية", "تاريخ النهاية", "عدد الأيام", "السبب", "الحالة")
        
        self.leaves_tree = ttk.Treeview(leaves_frame, columns=leaves_columns, show="headings", height=15)
        
        for col in leaves_columns:
            self.leaves_tree.heading(col, text=col)
            self.leaves_tree.column(col, width=120, anchor="center")
        
        self.leaves_tree.pack(fill="both", expand=True)
        
    def setup_performance_tab(self):
        """إعداد تبويب تقييم الأداء"""
        # شريط الأدوات
        toolbar_frame = ctk.CTkFrame(self.performance_tab)
        toolbar_frame.pack(fill="x", padx=10, pady=10)
        
        add_review_btn = ctk.CTkButton(
            toolbar_frame,
            text="➕ تقييم جديد",
            command=self.add_performance_review,
            width=120,
            height=35,
            fg_color=self.colors['success']
        )
        add_review_btn.pack(side="right", padx=5, pady=5)
        
        # جدول التقييمات
        performance_frame = ctk.CTkFrame(self.performance_tab)
        performance_frame.pack(fill="both", expand=True, padx=10, pady=10)
        
        performance_columns = ("الموظف", "فترة التقييم", "التقييم العام", "المهارات التقنية", "التواصل", "العمل الجماعي", "تاريخ التقييم")
        
        self.performance_tree = ttk.Treeview(performance_frame, columns=performance_columns, show="headings", height=15)
        
        for col in performance_columns:
            self.performance_tree.heading(col, text=col)
            self.performance_tree.column(col, width=120, anchor="center")
        
        self.performance_tree.pack(fill="both", expand=True)
        
    def load_employees(self):
        """تحميل قائمة الموظفين"""
        try:
            # مسح البيانات الحالية
            for item in self.employees_tree.get_children():
                self.employees_tree.delete(item)
            
            # جلب بيانات الموظفين
            query = """
                SELECT e.EmployeeID, e.FullName, d.DepartmentName, e.Position, 
                       e.Email, e.ContactInfo, e.HireDate, e.IsActive
                FROM Employees e
                LEFT JOIN Departments d ON e.DepartmentID = d.DepartmentID
                ORDER BY e.FullName
            """
            
            employees = self.db_manager.cursor.execute(query).fetchall()
            
            # إضافة البيانات للجدول
            for emp in employees:
                status = "نشط" if emp[7] else "غير نشط"
                hire_date = emp[6].strftime("%Y/%m/%d") if emp[6] else ""
                
                self.employees_tree.insert("", "end", values=(
                    emp[0], emp[1], emp[2] or "غير محدد", emp[3] or "غير محدد",
                    emp[4] or "", emp[5] or "", hire_date, status
                ))
                
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تحميل بيانات الموظفين:\n{str(e)}")
    
    def search_employees(self, event=None):
        """البحث في الموظفين"""
        search_term = self.search_var.get().lower()
        
        # مسح البيانات الحالية
        for item in self.employees_tree.get_children():
            self.employees_tree.delete(item)
        
        if not search_term:
            self.load_employees()
            return
        
        try:
            query = """
                SELECT e.EmployeeID, e.FullName, d.DepartmentName, e.Position, 
                       e.Email, e.ContactInfo, e.HireDate, e.IsActive
                FROM Employees e
                LEFT JOIN Departments d ON e.DepartmentID = d.DepartmentID
                WHERE LOWER(e.FullName) LIKE ? OR LOWER(d.DepartmentName) LIKE ?
                ORDER BY e.FullName
            """
            
            employees = self.db_manager.cursor.execute(query, (f'%{search_term}%', f'%{search_term}%')).fetchall()
            
            for emp in employees:
                status = "نشط" if emp[7] else "غير نشط"
                hire_date = emp[6].strftime("%Y/%m/%d") if emp[6] else ""
                
                self.employees_tree.insert("", "end", values=(
                    emp[0], emp[1], emp[2] or "غير محدد", emp[3] or "غير محدد",
                    emp[4] or "", emp[5] or "", hire_date, status
                ))
                
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في البحث:\n{str(e)}")
    
    def add_employee(self):
        """إضافة موظف جديد"""
        messagebox.showinfo("قيد التطوير", "ميزة إضافة موظف جديد قيد التطوير")
    
    def edit_employee(self, event=None):
        """تعديل موظف"""
        messagebox.showinfo("قيد التطوير", "ميزة تعديل الموظف قيد التطوير")
    
    def delete_employee(self):
        """حذف موظف"""
        messagebox.showinfo("قيد التطوير", "ميزة حذف الموظف قيد التطوير")
    
    def add_department(self):
        """إضافة قسم جديد"""
        messagebox.showinfo("قيد التطوير", "ميزة إضافة قسم جديد قيد التطوير")
    
    def load_attendance(self):
        """تحميل بيانات الحضور"""
        messagebox.showinfo("قيد التطوير", "ميزة تحميل الحضور قيد التطوير")
    
    def record_checkin(self):
        """تسجيل حضور"""
        messagebox.showinfo("قيد التطوير", "ميزة تسجيل الحضور قيد التطوير")
    
    def add_leave_request(self):
        """إضافة طلب إجازة"""
        messagebox.showinfo("قيد التطوير", "ميزة طلب الإجازة قيد التطوير")
    
    def add_performance_review(self):
        """إضافة تقييم أداء"""
        messagebox.showinfo("قيد التطوير", "ميزة تقييم الأداء قيد التطوير")
